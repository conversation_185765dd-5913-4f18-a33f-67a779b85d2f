"use client"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"
import { createIntersectionObserver, preloadImage } from "@/lib/cache"

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  quality?: number
  placeholder?: "blur" | "empty"
  blurDataURL?: string
  sizes?: string
  fill?: boolean
  lazy?: boolean
  onLoad?: () => void
  onError?: () => void
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  quality = 75,
  placeholder = "empty",
  blurDataURL,
  sizes,
  fill = false,
  lazy = true,
  onLoad,
  onError,
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [shouldLoad, setShouldLoad] = useState(!lazy || priority)
  const imgRef = useRef<HTMLDivElement>(null)

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || priority || shouldLoad) return

    const observer = createIntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setShouldLoad(true)
            observer?.unobserve(entry.target)
          }
        })
      },
      { rootMargin: "100px" }
    )

    if (observer && imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => {
      if (observer && imgRef.current) {
        observer.unobserve(imgRef.current)
      }
    }
  }, [lazy, priority, shouldLoad])

  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }

  const handleError = () => {
    setHasError(true)
    onError?.()
  }

  // Generate responsive sizes if not provided
  const responsiveSizes = sizes || (
    fill 
      ? "100vw"
      : "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
  )

  // Fallback image for errors
  const fallbackSrc = "/images/placeholder.jpg" // You should add this to your public folder

  if (hasError) {
    return (
      <div 
        ref={imgRef}
        className={cn(
          "bg-gray-200 flex items-center justify-center text-gray-500 text-sm",
          className
        )}
        style={{ width, height }}
      >
        Failed to load image
      </div>
    )
  }

  if (!shouldLoad) {
    return (
      <div 
        ref={imgRef}
        className={cn(
          "bg-gray-100 animate-pulse",
          className
        )}
        style={{ width, height }}
      />
    )
  }

  return (
    <div ref={imgRef} className={cn("relative overflow-hidden", className)}>
      <Image
        src={src}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        priority={priority}
        quality={quality}
        placeholder={placeholder}
        blurDataURL={blurDataURL}
        sizes={responsiveSizes}
        className={cn(
          "transition-opacity duration-300",
          isLoaded ? "opacity-100" : "opacity-0"
        )}
        onLoad={handleLoad}
        onError={handleError}
      />
      
      {/* Loading placeholder */}
      {!isLoaded && (
        <div className="absolute inset-0 bg-gray-100 animate-pulse flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin" />
        </div>
      )}
    </div>
  )
}

// Product image component with specific optimizations
interface ProductImageProps {
  src: string
  alt: string
  className?: string
  priority?: boolean
  size?: "sm" | "md" | "lg" | "xl"
}

export function ProductImage({
  src,
  alt,
  className,
  priority = false,
  size = "md"
}: ProductImageProps) {
  const sizeConfig = {
    sm: { width: 100, height: 100 },
    md: { width: 200, height: 200 },
    lg: { width: 300, height: 300 },
    xl: { width: 400, height: 400 },
  }

  const { width, height } = sizeConfig[size]

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={cn("rounded-lg object-cover", className)}
      priority={priority}
      quality={80}
      sizes={`${width}px`}
    />
  )
}

// Avatar component with optimizations
interface AvatarImageProps {
  src?: string
  alt: string
  size?: number
  className?: string
  fallback?: string
}

export function AvatarImage({
  src,
  alt,
  size = 40,
  className,
  fallback
}: AvatarImageProps) {
  const [hasError, setHasError] = useState(false)

  if (!src || hasError) {
    return (
      <div 
        className={cn(
          "bg-gray-300 rounded-full flex items-center justify-center text-gray-600 font-medium",
          className
        )}
        style={{ width: size, height: size, fontSize: size * 0.4 }}
      >
        {fallback || alt.charAt(0).toUpperCase()}
      </div>
    )
  }

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={cn("rounded-full object-cover", className)}
      quality={90}
      onError={() => setHasError(true)}
    />
  )
}

// Hero image component for banners
interface HeroImageProps {
  src: string
  alt: string
  className?: string
  priority?: boolean
  overlay?: boolean
}

export function HeroImage({
  src,
  alt,
  className,
  priority = true,
  overlay = false
}: HeroImageProps) {
  return (
    <div className={cn("relative w-full h-full", className)}>
      <OptimizedImage
        src={src}
        alt={alt}
        fill
        priority={priority}
        quality={85}
        className="object-cover"
        sizes="100vw"
      />
      {overlay && (
        <div className="absolute inset-0 bg-black bg-opacity-30" />
      )}
    </div>
  )
}

// Gallery image component with lazy loading
interface GalleryImageProps {
  src: string
  alt: string
  className?: string
  onClick?: () => void
}

export function GalleryImage({
  src,
  alt,
  className,
  onClick
}: GalleryImageProps) {
  return (
    <div 
      className={cn(
        "cursor-pointer transition-transform hover:scale-105",
        className
      )}
      onClick={onClick}
    >
      <OptimizedImage
        src={src}
        alt={alt}
        width={300}
        height={200}
        className="rounded-lg object-cover"
        quality={75}
        lazy={true}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 300px"
      />
    </div>
  )
}
