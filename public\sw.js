// Spaza Smart Order Service Worker
// Provides offline functionality and caching

const CACHE_NAME = 'spaza-smart-order-v1'
const STATIC_CACHE_NAME = 'spaza-static-v1'
const DYNAMIC_CACHE_NAME = 'spaza-dynamic-v1'

// Files to cache immediately (only existing routes)
const STATIC_FILES = [
  '/',
  '/login',
  '/sign-up',
  '/products',
  '/home',
  '/cart',
  '/offline',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  // Add other critical static assets
]

// API endpoints to cache
const API_CACHE_PATTERNS = [
  /^\/api\/products/,
  /^\/api\/categories/,
  /^\/api\/health/,
]

// Files that should always be fetched from network
const NETWORK_FIRST_PATTERNS = [
  /^\/api\/auth/,
  /^\/api\/orders/,
  /^\/api\/cart/,
  /^\/api\/payments/,
]

// Maximum cache size for dynamic content
const MAX_DYNAMIC_CACHE_SIZE = 50

// Install event - cache static files
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...')
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching static files')
        // Add each file individually to prevent the entire cache from failing
        return Promise.allSettled(
          STATIC_FILES.map(url => 
            fetch(url, { credentials: 'same-origin' })
              .then(response => {
                if (!response.ok) {
                  console.warn(`Service Worker: Failed to cache ${url} - ${response.status}`)
                  return Promise.resolve() // Skip this file instead of rejecting
                }
                return cache.put(url, response)
              })
              .catch(err => {
                console.warn(`Service Worker: Error caching ${url}`, err)
                return Promise.resolve() // Continue with other files
              })
          )
        )
      })
      .then(() => {
        console.log('Service Worker: Static files cached (with possible warnings)')
        return self.skipWaiting()
      })
      .catch((error) => {
        console.error('Service Worker: Error during installation', error)
      })
  )
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...')
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE_NAME && 
                cacheName !== DYNAMIC_CACHE_NAME &&
                cacheName !== CACHE_NAME) {
              console.log('Service Worker: Deleting old cache', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        console.log('Service Worker: Activated')
        return self.clients.claim()
      })
  )
})

// Fetch event - handle requests
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return
  }
  
  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return
  }
  
  event.respondWith(handleFetch(request))
})

async function handleFetch(request) {
  const url = new URL(request.url)
  
  try {
    // Network first for critical API endpoints
    if (NETWORK_FIRST_PATTERNS.some(pattern => pattern.test(url.pathname))) {
      return await networkFirst(request)
    }
    
    // Cache first for API endpoints that can be cached
    if (API_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname))) {
      return await cacheFirst(request)
    }
    
    // Cache first for static files
    if (STATIC_FILES.includes(url.pathname) || 
        url.pathname.startsWith('/_next/static/') ||
        url.pathname.startsWith('/icons/') ||
        url.pathname.startsWith('/images/')) {
      return await cacheFirst(request)
    }
    
    // Stale while revalidate for pages
    return await staleWhileRevalidate(request)
    
  } catch (error) {
    console.error('Service Worker: Fetch error', error)
    return await handleOffline(request)
  }
}

// Cache first strategy
async function cacheFirst(request) {
  const cachedResponse = await caches.match(request)
  
  if (cachedResponse) {
    return cachedResponse
  }
  
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME)
      cache.put(request, networkResponse.clone())
      await limitCacheSize(DYNAMIC_CACHE_NAME, MAX_DYNAMIC_CACHE_SIZE)
    }
    
    return networkResponse
  } catch (error) {
    return await handleOffline(request)
  }
}

// Network first strategy
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME)
      cache.put(request, networkResponse.clone())
      await limitCacheSize(DYNAMIC_CACHE_NAME, MAX_DYNAMIC_CACHE_SIZE)
    }
    
    return networkResponse
  } catch (error) {
    const cachedResponse = await caches.match(request)
    
    if (cachedResponse) {
      return cachedResponse
    }
    
    return await handleOffline(request)
  }
}

// Stale while revalidate strategy
async function staleWhileRevalidate(request) {
  const cachedResponse = await caches.match(request)
  
  const networkResponsePromise = fetch(request)
    .then(async (networkResponse) => {
      if (networkResponse.ok) {
        const cache = await caches.open(DYNAMIC_CACHE_NAME)
        cache.put(request, networkResponse.clone())
        await limitCacheSize(DYNAMIC_CACHE_NAME, MAX_DYNAMIC_CACHE_SIZE)
      }
      return networkResponse
    })
    .catch(() => null)
  
  return cachedResponse || await networkResponsePromise || await handleOffline(request)
}

// Handle offline scenarios
async function handleOffline(request) {
  const url = new URL(request.url)
  
  // Return offline page for navigation requests
  if (request.mode === 'navigate') {
    const offlineResponse = await caches.match('/offline')
    if (offlineResponse) {
      return offlineResponse
    }
  }
  
  // Return cached version if available
  const cachedResponse = await caches.match(request)
  if (cachedResponse) {
    return cachedResponse
  }
  
  // Return a basic offline response
  return new Response(
    JSON.stringify({
      error: 'Offline',
      message: 'You are currently offline. Please check your internet connection.',
    }),
    {
      status: 503,
      statusText: 'Service Unavailable',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  )
}

// Limit cache size
async function limitCacheSize(cacheName, maxSize) {
  const cache = await caches.open(cacheName)
  const keys = await cache.keys()
  
  if (keys.length > maxSize) {
    const keysToDelete = keys.slice(0, keys.length - maxSize)
    await Promise.all(keysToDelete.map(key => cache.delete(key)))
  }
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync', event.tag)
  
  if (event.tag === 'background-sync-orders') {
    event.waitUntil(syncOfflineOrders())
  }
  
  if (event.tag === 'background-sync-cart') {
    event.waitUntil(syncOfflineCart())
  }
})

// Sync offline orders when back online
async function syncOfflineOrders() {
  try {
    // Get offline orders from IndexedDB or localStorage
    const offlineOrders = await getOfflineOrders()
    
    for (const order of offlineOrders) {
      try {
        const response = await fetch('/api/orders', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(order),
        })
        
        if (response.ok) {
          await removeOfflineOrder(order.id)
          console.log('Service Worker: Synced offline order', order.id)
        }
      } catch (error) {
        console.error('Service Worker: Failed to sync order', order.id, error)
      }
    }
  } catch (error) {
    console.error('Service Worker: Background sync failed', error)
  }
}

// Sync offline cart when back online
async function syncOfflineCart() {
  try {
    // Implementation for syncing offline cart changes
    console.log('Service Worker: Syncing offline cart changes')
  } catch (error) {
    console.error('Service Worker: Cart sync failed', error)
  }
}

// Push notification handling
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push notification received')
  
  const options = {
    body: 'You have a new notification from Spaza Smart Order',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [200, 100, 200],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1,
    },
    actions: [
      {
        action: 'explore',
        title: 'View Details',
        icon: '/icons/checkmark.png',
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/icons/xmark.png',
      },
    ],
  }
  
  if (event.data) {
    const data = event.data.json()
    options.body = data.body || options.body
    options.data = { ...options.data, ...data }
  }
  
  event.waitUntil(
    self.registration.showNotification('Spaza Smart Order', options)
  )
})

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked')
  
  event.notification.close()
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/orders')
    )
  } else if (event.action === 'close') {
    // Just close the notification
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.openWindow('/')
    )
  }
})

// Helper functions for offline storage
async function getOfflineOrders() {
  // Implementation would use IndexedDB or localStorage
  return []
}

async function removeOfflineOrder(orderId) {
  // Implementation would remove from IndexedDB or localStorage
  console.log('Removing offline order:', orderId)
}
