"use client"

import { useState, useEffe<PERSON>, <PERSON>, JSXElementConstructor, ReactElement, ReactNode, ReactPortal } from "react"
import { useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Id } from "@/convex/_generated/dataModel"
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { toast } from "sonner"
import { CreditCard, MapPin, Clock, CheckCircle, Loader2, AlertCircle } from "lucide-react"
import { useConvexCart } from "@/hooks/use-convex-cart"
import { cn } from "@/lib/utils"

interface OrderItem {
  productId: Id<"products">
  name: string
  price: number
  quantity: number
  imageUrl?: string
}

interface FormData {
  deliveryAddress: string
  deliveryOption: 'standard' | 'express' | 'scheduled'
  paymentMethod: 'cash' | 'card'
  notes: string
  phoneNumber: string
  deliveryDate?: string
  deliveryTime?: string
}

const initialFormData: FormData = {
  deliveryAddress: '',
  deliveryOption: 'standard',
  paymentMethod: 'cash',
  notes: '',
  phoneNumber: '',
  deliveryDate: '',
  deliveryTime: ''
}

const deliveryOptions = [
  { id: 'standard', label: 'Standard Delivery', description: '2-3 business days', price: 15 },
  { id: 'express', label: 'Express Delivery', description: 'Next business day', price: 25 },
  { id: 'scheduled', label: 'Scheduled Delivery', description: 'Pick a date & time', price: 30 }
]

interface CheckoutFormProps {
  userId: string
  onOrderComplete?: (orderId: string) => void
}

export function CheckoutForm({ userId, onOrderComplete }: CheckoutFormProps) {
  const { selectedItems, totalPrice, loading } = useConvexCart(userId)
  const createOrder = useMutation(api.orders.create)
  const purchaseProduct = useMutation(api.products.purchaseProduct)
  const clearCart = useMutation(api.cart.clear)

  const [formData, setFormData] = useState({
    deliveryAddress: "",
    deliveryOption: "standard",
    paymentMethod: "cash",
    notes: ""
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (selectedItems.length === 0) {
      toast.error("Please select items to checkout")
      return
    }

    if (!formData.deliveryAddress.trim()) {
      toast.error("Please enter a delivery address")
      return
    }

    setIsSubmitting(true)

    try {
      // First, check stock availability and reduce stock
      for (const item of selectedItems) {
        if (item.product) {
          await purchaseProduct({
            productId: item.product._id,
            quantity: item.quantity
          })
        }
      }

      // Create the order
      const orderId = await createOrder({
        userId,
        items: selectedItems.map((item: { product: any; quantity: any }) => ({
          productId: item.product!._id,
          name: item.product!.name,
          price: item.product!.price,
          quantity: item.quantity,
          image: item.product!.image
        })),
        subtotal: totalPrice,
        deliveryFee: 15,
        paymentFees: 0,
        total: totalPrice + 15,
        deliveryAddress: formData.deliveryAddress,
        deliveryOption: formData.deliveryOption,
        paymentMethod: "cash_on_delivery",
        notes: formData.notes
      })

      // Clear selected items from cart
      await clearCart({ userId })

      toast.success("Order placed successfully!")
      
      if (onOrderComplete) {
        onOrderComplete(orderId)
      }

      // Reset form
      setFormData({
        deliveryAddress: "",
        deliveryOption: "standard",
        paymentMethod: "cash",
        notes: ""
      })

    } catch (error) {
      console.error("Error creating order:", error)
      toast.error("Failed to place order. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (selectedItems.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <CheckCircle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold mb-2">No items selected</h3>
          <p className="text-gray-500">Please select items from your cart to checkout.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Delivery Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Delivery Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="deliveryAddress">Delivery Address *</Label>
              <Textarea
                id="deliveryAddress"
                value={formData.deliveryAddress}
                onChange={(e) => handleInputChange("deliveryAddress", e.target.value)}
                placeholder="Enter your full delivery address..."
                required
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="deliveryOption">Delivery Option</Label>
              <Select
                value={formData.deliveryOption}
                onValueChange={(value) => handleInputChange("deliveryOption", value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="standard">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Standard (2-3 business days) - R15.00
                    </div>
                  </SelectItem>
                  <SelectItem value="express">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Express (Next business day) - R25.00
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="notes">Special Instructions (Optional)</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleInputChange("notes", e.target.value)}
                placeholder="Any special delivery instructions..."
                rows={2}
              />
            </div>
          </CardContent>
        </Card>

        {/* Payment & Order Summary */}
        <div className="space-y-6">
          {/* Payment Method */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Payment Method
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Select
                value={formData.paymentMethod}
                onValueChange={(value) => handleInputChange("paymentMethod", value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cash">Cash on Delivery</SelectItem>
                  <SelectItem value="card">Card Payment</SelectItem>
                  <SelectItem value="eft">EFT Transfer</SelectItem>
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                {selectedItems.map((item) => (
                  <div key={item._id} className="flex justify-between text-sm">
                    <span>{item.product?.name} x {item.quantity}</span>
                    <span>R{((item.product?.price || 0) * item.quantity).toFixed(2)}</span>
                  </div>
                ))}
              </div>
              
              <div className="border-t pt-2 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>R{totalPrice.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Delivery Fee</span>
                  <span>R{formData.deliveryOption === "express" ? "25.00" : "15.00"}</span>
                </div>
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span>R{(totalPrice + (formData.deliveryOption === "express" ? 25 : 15)).toFixed(2)}</span>
                </div>
              </div>

              <Button 
                type="submit" 
                className="w-full" 
                size="lg"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Processing...
                  </div>
                ) : (
                  <>
                    <CreditCard className="h-4 w-4 mr-2" />
                    Place Order
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </form>
  )
}
