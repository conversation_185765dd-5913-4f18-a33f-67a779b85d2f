import { mutation } from "./_generated/server"
import { v } from "convex/values"
import { hashPassword } from "./users"

export const createTestUser = mutation({
  args: {
    email: v.string(),
    password: v.string(),
    name: v.string(),
  },
  handler: async (ctx, args) => {
    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first()

    if (existingUser) {
      return { success: false, message: "User already exists", userId: existingUser._id }
    }

    // Create new user
    const passwordHash = await hashPassword(args.password)
    const userId = await ctx.db.insert("users", {
      email: args.email,
      name: args.name,
      passwordHash,
      role: "customer",
      isActive: true,
      emailVerified: true,
      membershipTier: "Bronze",
      loyaltyPoints: 0,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    })

    return { 
      success: true, 
      userId,
      user: {
        _id: userId,
        email: args.email,
        name: args.name,
        role: "customer",
        isActive: true,
        emailVerified: true
      }
    }
  },
})
