"use client"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { ArrowLeft, Globe, Mic, MicOff, Volume2, Check } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage, languageNames, type Language } from "@/contexts/language-context"

export default function LanguageSettingsPage() {
  const router = useRouter()
  const { language, setLanguage, t } = useLanguage()
  const [voiceEnabled, setVoiceEnabled] = useState(false)
  const [isListening, setIsListening] = useState(false)

  const languages: Array<{ code: Language; name: string; nativeName: string; region: string }> = [
    { code: "en", name: "English", nativeName: "English", region: "South Africa" },
    { code: "af", name: "Afrikaans", nativeName: "Afrikaans", region: "Western Cape" },
    { code: "zu", name: "Zulu", nativeName: "isiZulu", region: "KwaZulu-Natal" },
    { code: "xh", name: "Xhosa", nativeName: "isiXhosa", region: "Eastern Cape" },
    { code: "st", name: "Sotho", nativeName: "Sesotho", region: "Free State" },
  ]

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage)
  }

  const toggleVoiceOrdering = () => {
    setVoiceEnabled(!voiceEnabled)
    if (!voiceEnabled) {
      // Request microphone permission
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then(() => {
          console.log("Microphone access granted")
        })
        .catch(() => {
          console.log("Microphone access denied")
          setVoiceEnabled(false)
        })
    }
  }

  const startVoiceTest = () => {
    if (!voiceEnabled) return

    setIsListening(true)
    // Simulate voice recognition
    setTimeout(() => {
      setIsListening(false)
      alert(`Voice test completed in ${languageNames[language]}`)
    }, 3000)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4 flex items-center gap-3">
        <Button variant="ghost" size="sm" className="text-white hover:bg-blue-700 p-2" onClick={() => router.back()}>
          <ArrowLeft className="w-5 h-5" />
        </Button>
        <h1 className="text-lg font-semibold">{t("language.title")}</h1>
      </div>

      <div className="p-4 space-y-6">
        {/* Current Language */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="w-5 h-5 text-blue-600" />
              {t("language.current")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div>
                <p className="font-semibold text-blue-900">{languageNames[language]}</p>
                <p className="text-sm text-blue-700">{languages.find((l) => l.code === language)?.nativeName}</p>
              </div>
              <Badge className="bg-blue-600 text-white">Active</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Language Selection */}
        <Card>
          <CardHeader>
            <CardTitle>{t("language.select")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {languages.map((lang) => (
              <div
                key={lang.code}
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  language === lang.code
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                }`}
                onClick={() => handleLanguageChange(lang.code)}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-gray-900">{lang.name}</h3>
                    <p className="text-sm text-gray-600">{lang.nativeName}</p>
                    <p className="text-xs text-gray-500">{lang.region}</p>
                  </div>
                  {language === lang.code && <Check className="w-5 h-5 text-blue-600" />}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Voice Ordering */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mic className="w-5 h-5 text-blue-600" />
              {t("language.voiceOrdering")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-gray-900">{t("language.enableVoice")}</p>
                <p className="text-sm text-gray-600">
                  Order products using voice commands in {languageNames[language]}
                </p>
              </div>
              <Button
                onClick={toggleVoiceOrdering}
                variant={voiceEnabled ? "default" : "outline"}
                className={voiceEnabled ? "bg-green-600 hover:bg-green-700" : "bg-transparent"}
              >
                {voiceEnabled ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
              </Button>
            </div>

            {voiceEnabled && (
              <div className="space-y-3">
                <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-sm text-green-800">
                    Voice ordering is enabled for {languageNames[language]}. You can now use voice commands to search
                    and add products to your cart.
                  </p>
                </div>

                <Button
                  onClick={startVoiceTest}
                  disabled={isListening}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {isListening ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Listening...
                    </div>
                  ) : (
                    <>
                      <Volume2 className="w-4 h-4 mr-2" />
                      Test Voice Recognition
                    </>
                  )}
                </Button>

                <div className="text-xs text-gray-500 space-y-1">
                  <p>
                    <strong>Voice Commands:</strong>
                  </p>
                  <p>• "Add bread to cart" - Adds bread to your shopping cart</p>
                  <p>• "Search for milk" - Searches for milk products</p>
                  <p>• "Show my orders" - Opens your order history</p>
                  <p>• "Go to checkout" - Proceeds to checkout</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Regional Information */}
        <Card>
          <CardHeader>
            <CardTitle>Regional Preferences</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div className="p-3 bg-gray-50 rounded-lg">
                <h4 className="font-semibold text-gray-900">Currency & Pricing</h4>
                <p className="text-gray-600">All prices displayed in South African Rand (ZAR)</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <h4 className="font-semibold text-gray-900">Local Products</h4>
                <p className="text-gray-600">Product names and descriptions adapted for South African market</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <h4 className="font-semibold text-gray-900">Cultural Adaptation</h4>
                <p className="text-gray-600">Interface adapted for local customs and preferences</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Language Support Info */}
        <Card>
          <CardHeader>
            <CardTitle>Language Support</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-gray-600">
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <p>Full interface translation for all supported languages</p>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <p>Voice recognition and commands in your selected language</p>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <p>Customer support available in all supported languages</p>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <p>Localized product names and descriptions</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
