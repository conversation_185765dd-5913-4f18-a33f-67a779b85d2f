"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"

export type Language = "en" | "af" | "zu" | "xh" | "st"

// Define the translation key type based on the English translations
type TranslationKey = keyof typeof translations.en

export interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: TranslationKey) => string
  isRTL: boolean
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

// Keep the existing translations object as is
const translations = {
  en: {
    // Navigation
    "nav.home": "Home",
    "nav.cart": "Cart",
    "nav.orders": "Orders",
    "nav.profile": "Profile",
    // ... rest of the translations ...
  },
  af: { /* ... */ },
  zu: { /* ... */ },
  xh: { /* ... */ },
  st: { /* ... */ },
} as const // Add 'as const' to make the object readonly and preserve literal types

const languageNames = {
  en: "English",
  af: "Afrikaans",
  zu: "Zulu",
  xh: "Xhosa",
  st: "Sesotho",
}

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguageState] = useState<Language>("en")

  useEffect(() => {
    const savedLanguage = localStorage.getItem("spaza-language") as Language | null
    if (savedLanguage && Object.keys(translations).includes(savedLanguage)) {
      setLanguageState(savedLanguage)
    }
  }, [])

  const setLanguage = (lang: Language) => {
    setLanguageState(lang)
    localStorage.setItem("spaza-language", lang)
    document.documentElement.lang = lang
  }

  const t = (key: string): string => {
    return (translations[language] as any)[key] || key
  }

  const isRTL = false // None of the South African languages are RTL

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, isRTL }}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}

export { languageNames }
