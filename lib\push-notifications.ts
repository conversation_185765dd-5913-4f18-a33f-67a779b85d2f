// Push notification service for PWA

interface NotificationPayload {
  title: string
  body: string
  icon?: string
  badge?: string
  image?: string
  data?: any
  actions?: NotificationAction[]
  tag?: string
  requireInteraction?: boolean
}

interface PushSubscriptionData {
  endpoint: string
  keys: {
    p256dh: string
    auth: string
  }
}

class PushNotificationService {
  private vapidPublicKey: string
  private serviceWorkerRegistration: ServiceWorkerRegistration | null = null

  constructor() {
    // In production, this should come from environment variables
    this.vapidPublicKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || ''
  }

  // Initialize push notifications
  async initialize(): Promise<boolean> {
    if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
      console.warn('Push notifications are not supported')
      return false
    }

    try {
      // Register service worker
      this.serviceWorkerRegistration = await navigator.serviceWorker.register('/sw.js')
      console.log('Service Worker registered successfully')

      // Wait for service worker to be ready
      await navigator.serviceWorker.ready
      
      return true
    } catch (error) {
      console.error('Service Worker registration failed:', error)
      return false
    }
  }

  // Request notification permission
  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      console.warn('Notifications are not supported')
      return 'denied'
    }

    let permission = Notification.permission

    if (permission === 'default') {
      permission = await Notification.requestPermission()
    }

    return permission
  }

  // Subscribe to push notifications
  async subscribe(): Promise<PushSubscriptionData | null> {
    if (!this.serviceWorkerRegistration) {
      console.error('Service Worker not registered')
      return null
    }

    try {
      const permission = await this.requestPermission()
      
      if (permission !== 'granted') {
        console.warn('Notification permission not granted')
        return null
      }

      // Convert VAPID key to Uint8Array
      const applicationServerKey = this.urlBase64ToUint8Array(this.vapidPublicKey)

      const subscription = await this.serviceWorkerRegistration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey,
      })

      const subscriptionData: PushSubscriptionData = {
        endpoint: subscription.endpoint,
        keys: {
          p256dh: this.arrayBufferToBase64(subscription.getKey('p256dh')!),
          auth: this.arrayBufferToBase64(subscription.getKey('auth')!),
        },
      }

      // Send subscription to server
      await this.sendSubscriptionToServer(subscriptionData)

      return subscriptionData
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error)
      return null
    }
  }

  // Unsubscribe from push notifications
  async unsubscribe(): Promise<boolean> {
    if (!this.serviceWorkerRegistration) {
      return false
    }

    try {
      const subscription = await this.serviceWorkerRegistration.pushManager.getSubscription()
      
      if (subscription) {
        await subscription.unsubscribe()
        await this.removeSubscriptionFromServer(subscription.endpoint)
        console.log('Unsubscribed from push notifications')
        return true
      }
      
      return false
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error)
      return false
    }
  }

  // Check if user is subscribed
  async isSubscribed(): Promise<boolean> {
    if (!this.serviceWorkerRegistration) {
      return false
    }

    try {
      const subscription = await this.serviceWorkerRegistration.pushManager.getSubscription()
      return subscription !== null
    } catch (error) {
      console.error('Failed to check subscription status:', error)
      return false
    }
  }

  // Show local notification
  async showNotification(payload: NotificationPayload): Promise<void> {
    if (!this.serviceWorkerRegistration) {
      console.error('Service Worker not registered')
      return
    }

    const permission = await this.requestPermission()
    
    if (permission !== 'granted') {
      console.warn('Notification permission not granted')
      return
    }

    const options: NotificationOptions = {
      body: payload.body,
      icon: payload.icon || '/icons/icon-192x192.png',
      badge: payload.badge || '/icons/badge-72x72.png',
      image: payload.image,
      data: payload.data,
      actions: payload.actions,
      tag: payload.tag,
      requireInteraction: payload.requireInteraction || false,
      vibrate: [200, 100, 200],
    }

    await this.serviceWorkerRegistration.showNotification(payload.title, options)
  }

  // Send subscription to server
  private async sendSubscriptionToServer(subscription: PushSubscriptionData): Promise<void> {
    try {
      const response = await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscription),
      })

      if (!response.ok) {
        throw new Error('Failed to send subscription to server')
      }

      console.log('Subscription sent to server successfully')
    } catch (error) {
      console.error('Failed to send subscription to server:', error)
    }
  }

  // Remove subscription from server
  private async removeSubscriptionFromServer(endpoint: string): Promise<void> {
    try {
      const response = await fetch('/api/push/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ endpoint }),
      })

      if (!response.ok) {
        throw new Error('Failed to remove subscription from server')
      }

      console.log('Subscription removed from server successfully')
    } catch (error) {
      console.error('Failed to remove subscription from server:', error)
    }
  }

  // Utility functions
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - (base64String.length % 4)) % 4)
    const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/')
    
    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)
    
    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    
    return outputArray
  }

  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    
    return window.btoa(binary)
  }
}

// Create singleton instance
export const pushNotificationService = new PushNotificationService()

// React hook for push notifications
export function usePushNotifications() {
  const [isSupported, setIsSupported] = useState(false)
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [permission, setPermission] = useState<NotificationPermission>('default')
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const checkSupport = async () => {
      const supported = 'serviceWorker' in navigator && 'PushManager' in window
      setIsSupported(supported)

      if (supported) {
        await pushNotificationService.initialize()
        const subscribed = await pushNotificationService.isSubscribed()
        setIsSubscribed(subscribed)
        setPermission(Notification.permission)
      }
    }

    checkSupport()
  }, [])

  const subscribe = async (): Promise<boolean> => {
    setIsLoading(true)
    
    try {
      const subscription = await pushNotificationService.subscribe()
      const success = subscription !== null
      setIsSubscribed(success)
      setPermission(Notification.permission)
      return success
    } catch (error) {
      console.error('Failed to subscribe:', error)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const unsubscribe = async (): Promise<boolean> => {
    setIsLoading(true)
    
    try {
      const success = await pushNotificationService.unsubscribe()
      setIsSubscribed(!success)
      return success
    } catch (error) {
      console.error('Failed to unsubscribe:', error)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const showNotification = async (payload: NotificationPayload): Promise<void> => {
    await pushNotificationService.showNotification(payload)
  }

  return {
    isSupported,
    isSubscribed,
    permission,
    isLoading,
    subscribe,
    unsubscribe,
    showNotification,
  }
}

// Predefined notification templates
export const NotificationTemplates = {
  orderConfirmed: (orderNumber: string): NotificationPayload => ({
    title: 'Order Confirmed!',
    body: `Your order #${orderNumber} has been confirmed and is being prepared.`,
    icon: '/icons/icon-192x192.png',
    tag: `order-${orderNumber}`,
    data: { type: 'order_confirmed', orderNumber },
    actions: [
      {
        action: 'view_order',
        title: 'View Order',
        icon: '/icons/view.png',
      },
    ],
  }),

  orderReady: (orderNumber: string): NotificationPayload => ({
    title: 'Order Ready for Pickup!',
    body: `Your order #${orderNumber} is ready for collection.`,
    icon: '/icons/icon-192x192.png',
    tag: `order-${orderNumber}`,
    requireInteraction: true,
    data: { type: 'order_ready', orderNumber },
    actions: [
      {
        action: 'view_order',
        title: 'View Details',
        icon: '/icons/view.png',
      },
    ],
  }),

  newPromotion: (title: string, description: string): NotificationPayload => ({
    title: `🎉 ${title}`,
    body: description,
    icon: '/icons/icon-192x192.png',
    tag: 'promotion',
    data: { type: 'promotion' },
    actions: [
      {
        action: 'view_deals',
        title: 'View Deals',
        icon: '/icons/deals.png',
      },
    ],
  }),
}

// React imports
import { useState, useEffect } from 'react'
