import { mutation } from "./_generated/server"
import { v } from "convex/values"

// Simple password hashing function (same as in users.ts)
function hashPassword(password: string): string {
  return Buffer.from(password + "spaza_salt").toString('base64')
}

export const seedUsers = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if users already exist
    const existingUsers = await ctx.db.query("users").collect()
    if (existingUsers.length > 0) {
      console.log("Users already exist, skipping seed")
      return { message: "Users already exist" }
    }

    const now = Date.now()

    // Create sample users
    const users = [
      {
        email: "<EMAIL>",
        name: "<PERSON> Customer",
        phone: "+27123456789",
        passwordHash: hashPassword("password123"),
        role: "customer" as const,
        membershipTier: "Bronze",
        loyaltyPoints: 0,
        isActive: true,
        emailVerified: true,
        createdAt: now,
        updatedAt: now,
      },
      {
        email: "<EMAIL>",
        name: "<PERSON> Owner",
        phone: "+27987654321",
        passwordHash: hashPassword("password123"),
        role: "shop_owner" as const,
        membershipTier: "Gold",
        loyaltyPoints: 500,
        isActive: true,
        emailVerified: true,
        createdAt: now,
        updatedAt: now,
      },
      {
        email: "<EMAIL>",
        name: "Admin User",
        phone: "+27555123456",
        passwordHash: hashPassword("admin123"),
        role: "admin" as const,
        membershipTier: "Platinum",
        loyaltyPoints: 1000,
        isActive: true,
        emailVerified: true,
        createdAt: now,
        updatedAt: now,
      }
    ]

    const createdUsers = []
    for (const user of users) {
      const userId = await ctx.db.insert("users", user)
      createdUsers.push({ id: userId, email: user.email, role: user.role })
    }

    return { 
      message: "Sample users created successfully",
      users: createdUsers
    }
  },
})

export const clearUsers = mutation({
  args: {},
  handler: async (ctx) => {
    const users = await ctx.db.query("users").collect()
    for (const user of users) {
      await ctx.db.delete(user._id)
    }
    return { message: `Deleted ${users.length} users` }
  },
})
