"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Eye, EyeOff, ShoppingCart } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { useSecurity, useSecureForm } from "@/components/security/security-provider"
import { loginSchema, validateInput, authRateLimiter } from "@/lib/validation"
import { toast } from "sonner"

export default function LoginPage() {
  const router = useRouter()
  const { login, user, loading } = useAuth()
  const { checkRateLimit } = useSecurity()
  const { csrfToken, createSecureFormData } = useSecureForm()
  const [showPassword, setShowPassword] = useState(false)
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [rememberMe, setRememberMe] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<string[]>([])

  // Redirect if already logged in
  useEffect(() => {
    if (user && !loading) {
      router.push("/home")
    }
  }, [user, loading, router])

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setErrors([])

    // Check rate limiting
    const clientId = `login_${email || 'unknown'}`
    const { allowed } = checkRateLimit(clientId)

    if (!allowed) {
      toast.error("Too many login attempts. Please try again later.")
      return
    }

    // Validate input
    const validation = validateInput(loginSchema, { email, password })
    if (!validation.success) {
      setErrors(validation.errors)
      return
    }

    try {
      setIsLoading(true)

      // Create secure form data
      const secureData = createSecureFormData({
        email: validation.data.email,
        password: validation.data.password,
      })

      await login(secureData.email, secureData.password)

      // Redirect to home page
      router.push("/home")
    } catch (error) {
      console.error('Login failed:', error)
      // Error is already handled by the auth context with toast
    } finally {
      setIsLoading(false)
    }
  }

  const handleSignUp = () => {
    router.push("/sign-up")
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4 text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <ShoppingCart className="w-8 h-8" />
          <h1 className="text-2xl font-bold">Spaza Smart Order</h1>
        </div>
        <p className="text-blue-100 text-sm">Your neighborhood grocery delivery</p>
      </div>

      {/* Login Form */}
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-sm">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-2xl font-bold text-gray-900 text-center mb-6">Welcome Back</h2>

            {errors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                <ul className="text-sm text-red-600 space-y-1">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}

            <form onSubmit={handleLogin} className="space-y-4">
              <input type="hidden" name="csrf_token" value={csrfToken} />
              <div>
                <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                  Email Address
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="mt-1"
                  required
                />
              </div>

              <div>
                <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                  Password
                </Label>
                <div className="relative mt-1">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    className="pr-10"
                    required
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Checkbox
                    id="remember"
                    checked={rememberMe}
                    onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                  />
                  <Label htmlFor="remember" className="ml-2 text-sm text-gray-600">
                    Remember me
                  </Label>
                </div>
                <Button variant="link" className="text-sm text-blue-600 hover:text-blue-700 p-0">
                  Forgot password?
                </Button>
              </div>

              <Button
                type="submit"
                disabled={isLoading || loading}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg font-medium disabled:opacity-50"
              >
                {isLoading || loading ? "Signing In..." : "Sign In"}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Don't have an account?{" "}
                <Button variant="link" className="text-blue-600 hover:text-blue-700 p-0" onClick={handleSignUp}>
                  Sign up
                </Button>
              </p>
            </div>
          </div>

          {/* Additional Options */}
          <div className="mt-6 space-y-3">
            <Button variant="outline" className="w-full py-3 bg-transparent">
              Continue with Google
            </Button>
            <Button variant="outline" className="w-full py-3 bg-transparent">
              Continue with Facebook
            </Button>
          </div>

          {/* Footer */}
          <div className="mt-8 text-center text-xs text-gray-500">
            <p>By signing in, you agree to our</p>
            <div className="flex justify-center gap-4 mt-1">
              <Button variant="link" className="text-xs text-gray-500 p-0 h-auto">
                Terms of Service
              </Button>
              <Button variant="link" className="text-xs text-gray-500 p-0 h-auto">
                Privacy Policy
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
