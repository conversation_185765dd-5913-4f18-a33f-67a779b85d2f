"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, Phone, MessageCircle, Mail, Clock, HelpCircle, FileText, Star, ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import BottomNavigation from "@/components/bottom-navigation"

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
}

export default function SupportPage() {
  const router = useRouter()
  const [cartCount] = useState(5)
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null)

  const handleContact = (method: string) => {
    switch (method) {
      case "phone":
        window.location.href = "tel:+***********"
        break
      case "whatsapp":
        const whatsappMessage = encodeURIComponent("Hi! I need help with my Spaza Smart Order account.")
        window.open(`https://wa.me/***********?text=${whatsappMessage}`, "_blank")
        break
      case "email":
        const emailSubject = encodeURIComponent("Spaza Smart Order - Support Request")
        const emailBody = encodeURIComponent(
          "Hi Support Team,\n\nI need assistance with:\n\n[Please describe your issue here]\n\nThank you!",
        )
        window.location.href = `mailto:<EMAIL>?subject=${emailSubject}&body=${emailBody}`
        break
    }
  }

  const contactMethods = [
    {
      id: "phone",
      title: "Call Us",
      description: "Speak with our support team",
      detail: "+27 11 123 4567",
      icon: Phone,
      available: "24/7 Available",
      color: "bg-green-50 text-green-600",
    },
    {
      id: "whatsapp",
      title: "WhatsApp",
      description: "Chat with us on WhatsApp",
      detail: "+27 82 123 4567",
      icon: MessageCircle,
      available: "Mon-Fri, 8AM-6PM",
      color: "bg-green-50 text-green-600",
    },
    {
      id: "email",
      title: "Email Support",
      description: "Send us your questions",
      detail: "<EMAIL>",
      icon: Mail,
      available: "Response within 24hrs",
      color: "bg-blue-50 text-blue-600",
    },
  ]

  const supportCategories = [
    {
      id: "orders",
      title: "Order Issues",
      description: "Track, modify, or cancel orders",
      icon: FileText,
      count: 12,
    },
    {
      id: "delivery",
      title: "Delivery Support",
      description: "Delivery times and locations",
      icon: Clock,
      count: 8,
    },
    {
      id: "account",
      title: "Account Help",
      description: "Profile and payment issues",
      icon: HelpCircle,
      count: 6,
    },
    {
      id: "feedback",
      title: "Feedback",
      description: "Share your experience",
      icon: Star,
      count: 0,
    },
  ]

  const faqs: FAQItem[] = [
    {
      id: "1",
      question: "How can I track my order?",
      answer:
        "You can track your order by going to 'My Orders' and clicking the 'Track Order' button. You'll see real-time updates on your order status and estimated delivery time.",
      category: "orders",
    },
    {
      id: "2",
      question: "What are your delivery hours?",
      answer:
        "We deliver Monday to Sunday from 8:00 AM to 8:00 PM. Same-day delivery is available for orders placed before 2:00 PM.",
      category: "delivery",
    },
    {
      id: "3",
      question: "How do I cancel my order?",
      answer:
        "You can cancel your order within 30 minutes of placing it by going to 'My Orders' and selecting 'Cancel Order'. After this time, please contact our support team.",
      category: "orders",
    },
    {
      id: "4",
      question: "What payment methods do you accept?",
      answer: "We accept all major credit cards, debit cards, EFT, and mobile payments including SnapScan and Zapper.",
      category: "account",
    },
    {
      id: "5",
      question: "Do you deliver to my area?",
      answer:
        "We currently deliver to Johannesburg, Pretoria, Cape Town, and Durban. Enter your address during checkout to confirm delivery availability.",
      category: "delivery",
    },
  ]

  const toggleFAQ = (faqId: string) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="flex items-center gap-3">
          <Button variant="ghost" size="sm" className="text-white hover:bg-blue-700 p-2" onClick={() => router.back()}>
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="text-lg font-semibold">Help & Support</h1>
            <p className="text-blue-100 text-sm">We're here to help you</p>
          </div>
        </div>
      </div>

      {/* Quick Contact */}
      <div className="bg-white border-b border-gray-200 p-4">
        <h2 className="font-semibold text-gray-900 mb-3">Contact Us</h2>
        <div className="space-y-3">
          {contactMethods.map((method) => (
            <div key={method.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-lg ${method.color}`}>
                  <method.icon className="w-5 h-5" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{method.title}</h3>
                  <p className="text-sm text-gray-600">{method.description}</p>
                  <p className="text-xs text-gray-500">{method.available}</p>
                </div>
              </div>
              <Button variant="outline" size="sm" onClick={() => handleContact(method.id)}>
                Contact
              </Button>
            </div>
          ))}
        </div>
      </div>

      {/* Support Categories */}
      <div className="bg-white border-b border-gray-200 p-4">
        <h2 className="font-semibold text-gray-900 mb-3">Browse Help Topics</h2>
        <div className="grid grid-cols-2 gap-3">
          {supportCategories.map((category) => (
            <div key={category.id} className="p-3 border border-gray-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <category.icon className="w-5 h-5 text-blue-600" />
                <h3 className="font-medium text-gray-900 text-sm">{category.title}</h3>
              </div>
              <p className="text-xs text-gray-600 mb-2">{category.description}</p>
              {category.count > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {category.count} articles
                </Badge>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* FAQ Section */}
      <div className="bg-white p-4">
        <h2 className="font-semibold text-gray-900 mb-3">Frequently Asked Questions</h2>
        <div className="space-y-3">
          {faqs.map((faq) => (
            <div key={faq.id} className="border border-gray-200 rounded-lg">
              <button
                className="w-full p-3 text-left flex items-center justify-between"
                onClick={() => toggleFAQ(faq.id)}
              >
                <h3 className="font-medium text-gray-900 text-sm">{faq.question}</h3>
                <ChevronRight
                  className={`w-4 h-4 text-gray-500 transition-transform ${expandedFAQ === faq.id ? "rotate-90" : ""}`}
                />
              </button>
              {expandedFAQ === faq.id && (
                <div className="px-3 pb-3">
                  <p className="text-sm text-gray-600">{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Emergency Contact */}
      <div className="bg-red-50 border border-red-200 rounded-lg m-4 p-4">
        <div className="flex items-center gap-2 mb-2">
          <Phone className="w-5 h-5 text-red-600" />
          <h3 className="font-semibold text-red-900">Emergency Support</h3>
        </div>
        <p className="text-sm text-red-800 mb-3">
          For urgent delivery issues or order problems, call our emergency line:
        </p>
        <Button
          className="bg-red-600 hover:bg-red-700 text-white w-full"
          onClick={() => (window.location.href = "tel:+***********")}
        >
          Call Emergency: +27 11 911 0000
        </Button>
      </div>

      <BottomNavigation cartCount={cartCount} />

      {/* Bottom padding to account for fixed navigation */}
      <div className="h-20"></div>
    </div>
  )
}
