"use client"

import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useState, useEffect } from "react"
import { toast } from "sonner"

export function useConvexOrders(userId: string, status?: string) {
  const [error, setError] = useState<string | null>(null)
  const orders = useQuery(api.orders.list, { userId, status })

  useEffect(() => {
    setError(null)
  }, [userId, status])

  useEffect(() => {
    if (orders === undefined) {
      return
    }

    if (orders === null) {
      const errorMessage = "Failed to load orders"
      setError(errorMessage)
      toast.error(errorMessage)
    }
  }, [orders])

  return {
    orders: orders || [],
    loading: orders === undefined,
    error,
    retry: () => {
      setError(null)
    }
  }
}


