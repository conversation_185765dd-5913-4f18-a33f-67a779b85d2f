import { NextRequest, NextResponse } from "next/server"

interface HealthCheck {
  status: "healthy" | "unhealthy"
  timestamp: string
  uptime: number
  version: string
  environment: string
  services: {
    convex: "healthy" | "unhealthy" | "unknown"
    database: "healthy" | "unhealthy" | "unknown"
    cache: "healthy" | "unhealthy" | "unknown"
  }
  metrics: {
    memoryUsage: NodeJS.MemoryUsage
    cpuUsage: number
    responseTime: number
  }
}

async function checkConvexHealth(): Promise<"healthy" | "unhealthy" | "unknown"> {
  try {
    // In a real implementation, you would check Convex connectivity
    // For now, we'll assume it's healthy if the environment variable is set
    return process.env.NEXT_PUBLIC_CONVEX_URL ? "healthy" : "unknown"
  } catch (error) {
    console.error("Convex health check failed:", error)
    return "unhealthy"
  }
}

async function checkDatabaseHealth(): Promise<"healthy" | "unhealthy" | "unknown"> {
  try {
    // Since we're using Convex as our database, this is the same as Convex health
    return await checkConvexHealth()
  } catch (error) {
    console.error("Database health check failed:", error)
    return "unhealthy"
  }
}

async function checkCacheHealth(): Promise<"healthy" | "unhealthy" | "unknown"> {
  try {
    // Check if Redis is available (if using Redis for caching)
    // For now, we'll check if the cache utilities are working
    return "healthy" // Memory cache is always available
  } catch (error) {
    console.error("Cache health check failed:", error)
    return "unhealthy"
  }
}

function getCpuUsage(): number {
  const startUsage = process.cpuUsage()
  const startTime = process.hrtime()
  
  // Simulate some work
  const endTime = process.hrtime(startTime)
  const endUsage = process.cpuUsage(startUsage)
  
  const totalTime = endTime[0] * 1000000 + endTime[1] / 1000 // Convert to microseconds
  const totalCpuTime = endUsage.user + endUsage.system
  
  return (totalCpuTime / totalTime) * 100
}

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    // Check all services
    const [convexHealth, databaseHealth, cacheHealth] = await Promise.all([
      checkConvexHealth(),
      checkDatabaseHealth(),
      checkCacheHealth(),
    ])

    const responseTime = Date.now() - startTime
    const memoryUsage = process.memoryUsage()
    const cpuUsage = getCpuUsage()

    // Determine overall health
    const allServicesHealthy = [convexHealth, databaseHealth, cacheHealth].every(
      status => status === "healthy"
    )

    const healthCheck: HealthCheck = {
      status: allServicesHealthy ? "healthy" : "unhealthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || "unknown",
      environment: process.env.NODE_ENV || "unknown",
      services: {
        convex: convexHealth,
        database: databaseHealth,
        cache: cacheHealth,
      },
      metrics: {
        memoryUsage,
        cpuUsage,
        responseTime,
      },
    }

    // Return appropriate status code
    const statusCode = healthCheck.status === "healthy" ? 200 : 503

    return NextResponse.json(healthCheck, {
      status: statusCode,
      headers: {
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Content-Type": "application/json",
      },
    })

  } catch (error) {
    console.error("Health check failed:", error)
    
    const errorResponse: HealthCheck = {
      status: "unhealthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || "unknown",
      environment: process.env.NODE_ENV || "unknown",
      services: {
        convex: "unknown",
        database: "unknown",
        cache: "unknown",
      },
      metrics: {
        memoryUsage: process.memoryUsage(),
        cpuUsage: 0,
        responseTime: Date.now() - startTime,
      },
    }

    return NextResponse.json(errorResponse, {
      status: 503,
      headers: {
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Content-Type": "application/json",
      },
    })
  }
}

// HEAD request for simple health checks
export async function HEAD() {
  try {
    const convexHealth = await checkConvexHealth()
    const isHealthy = convexHealth === "healthy"
    
    return new NextResponse(null, {
      status: isHealthy ? 200 : 503,
      headers: {
        "Cache-Control": "no-cache, no-store, must-revalidate",
      },
    })
  } catch (error) {
    return new NextResponse(null, {
      status: 503,
      headers: {
        "Cache-Control": "no-cache, no-store, must-revalidate",
      },
    })
  }
}
