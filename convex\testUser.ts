import { mutation } from "./_generated/server"
import { v } from "convex/values"

// Simple password hashing function (same as in users.ts)
function hashPassword(password: string): string {
  // In a real app, use bcrypt or similar
  return password
}

export const createTestUser = mutation({
  args: {
    email: v.string(),
    password: v.string(),
    name: v.string(),
  },
  handler: async (ctx, args) => {
    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first()

    if (existingUser) {
      return { success: false, message: "User already exists" }
    }

    // Create new user
    const userId = await ctx.db.insert("users", {
      email: args.email,
      name: args.name,
      passwordHash: hashPassword(args.password),
      role: "customer",
      isActive: true,
      emailVerified: true,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    })

    return { 
      success: true, 
      userId,
      user: {
        _id: userId,
        email: args.email,
        name: args.name,
        role: "customer",
        isActive: true,
        emailVerified: true
      }
    }
  },
})
