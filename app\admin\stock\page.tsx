"use client"

import { Label } from "@/components/ui/label"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  Search,
  Plus,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Package,
  Clock,
  BarChart3,
  Download,
  RefreshCw,
  Eye,
} from "lucide-react"

export default function StockTracking() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedFilter, setSelectedFilter] = useState("all")
  const [showAdjustModal, setShowAdjustModal] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<any>(null)

  const stockMovements = [
    {
      id: "1",
      productName: "White Star Maize Meal 5kg",
      sku: "WSM-5KG-001",
      type: "sale",
      quantity: -2,
      previousStock: 5,
      newStock: 3,
      timestamp: "2024-01-15 14:30",
      reference: "ORD-001",
      user: "System",
    },
    {
      id: "2",
      productName: "Coca-Cola Can 300ml",
      sku: "CC-300ML-001",
      type: "restock",
      quantity: +24,
      previousStock: 12,
      newStock: 36,
      timestamp: "2024-01-15 09:15",
      reference: "PO-2024-001",
      user: "Admin",
    },
    {
      id: "3",
      productName: "Lay's Potato Chips 120g",
      sku: "LAY-120G-001",
      type: "adjustment",
      quantity: -3,
      previousStock: 48,
      newStock: 45,
      timestamp: "2024-01-14 16:45",
      reference: "ADJ-001",
      user: "Manager",
    },
    {
      id: "4",
      productName: "Sunlight Dishwashing Liquid 750ml",
      sku: "SUN-750ML-001",
      type: "sale",
      quantity: -1,
      previousStock: 3,
      newStock: 2,
      timestamp: "2024-01-14 11:20",
      reference: "ORD-002",
      user: "System",
    },
  ]

  const criticalStockItems = [
    {
      id: "1",
      name: "Sunlight Dishwashing Liquid 750ml",
      sku: "SUN-750ML-001",
      currentStock: 2,
      minStock: 8,
      maxStock: 50,
      lastRestock: "2024-01-10",
      supplier: "Unilever",
      cost: 18.0,
      value: 36.0,
      daysLeft: 1,
      status: "critical",
    },
    {
      id: "2",
      name: "White Star Maize Meal 5kg",
      sku: "WSM-5KG-001",
      currentStock: 3,
      minStock: 10,
      maxStock: 100,
      lastRestock: "2024-01-08",
      supplier: "Tiger Brands",
      cost: 45.0,
      value: 135.0,
      daysLeft: 2,
      status: "critical",
    },
    {
      id: "3",
      name: "Coca-Cola Can 300ml",
      sku: "CC-300ML-001",
      currentStock: 12,
      minStock: 50,
      maxStock: 200,
      lastRestock: "2024-01-15",
      supplier: "Coca-Cola",
      cost: 6.0,
      value: 72.0,
      daysLeft: 5,
      status: "low",
    },
  ]

  const getMovementIcon = (type: string) => {
    switch (type) {
      case "sale":
        return <TrendingDown className="h-4 w-4 text-red-600" />
      case "restock":
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case "adjustment":
        return <RefreshCw className="h-4 w-4 text-blue-600" />
      default:
        return <Package className="h-4 w-4 text-gray-600" />
    }
  }

  const getMovementColor = (type: string) => {
    switch (type) {
      case "sale":
        return "text-red-600"
      case "restock":
        return "text-green-600"
      case "adjustment":
        return "text-blue-600"
      default:
        return "text-gray-600"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "critical":
        return "bg-red-100 text-red-700 border-red-200"
      case "low":
        return "bg-orange-100 text-orange-700 border-orange-200"
      default:
        return "bg-gray-100 text-gray-700 border-gray-200"
    }
  }

  const handleStockAdjustment = (product: any) => {
    setSelectedProduct(product)
    setShowAdjustModal(true)
  }

  const handleReorder = (product: any) => {
    // Simulate reorder process
    alert(`Reorder initiated for ${product.name}`)
  }

  const filteredMovements = stockMovements.filter((movement) => {
    const matchesSearch =
      movement.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      movement.sku.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesFilter = selectedFilter === "all" || movement.type === selectedFilter
    return matchesSearch && matchesFilter
  })

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button variant="ghost" size="sm" onClick={() => router.back()}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Stock Level Tracking</h1>
                <p className="text-sm text-gray-600">Monitor inventory levels and movements</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-1" />
                Stock Adjustment
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="p-4 space-y-6">
        {/* Stock Overview Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="border-0 shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">Total Items</p>
                  <p className="text-2xl font-bold text-gray-900">247</p>
                </div>
                <div className="bg-blue-100 p-3 rounded-lg">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">Critical Stock</p>
                  <p className="text-2xl font-bold text-red-600">8</p>
                </div>
                <div className="bg-red-100 p-3 rounded-lg">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">Stock Value</p>
                  <p className="text-2xl font-bold text-green-600">R 45k</p>
                </div>
                <div className="bg-green-100 p-3 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">Movements Today</p>
                  <p className="text-2xl font-bold text-purple-600">23</p>
                </div>
                <div className="bg-purple-100 p-3 rounded-lg">
                  <Clock className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Critical Stock Alert */}
        <Card className="border-0 shadow-sm border-l-4 border-l-red-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
              Critical Stock Levels
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {criticalStockItems.map((item) => (
                <div key={item.id} className="flex items-center justify-between p-4 bg-red-50 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-gray-900 text-sm">{item.name}</h3>
                      <Badge className={`text-xs ${getStatusColor(item.status)}`}>
                        {item.status === "critical" ? "Critical" : "Low Stock"}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-600">
                      <div>
                        <span className="font-medium">Current: </span>
                        <span className="text-red-600 font-bold">{item.currentStock}</span>
                      </div>
                      <div>
                        <span className="font-medium">Min: </span>
                        <span>{item.minStock}</span>
                      </div>
                      <div>
                        <span className="font-medium">Days Left: </span>
                        <span className="text-red-600 font-bold">{item.daysLeft}</span>
                      </div>
                      <div>
                        <span className="font-medium">Value: </span>
                        <span>R {item.value.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleStockAdjustment(item)}
                      className="bg-transparent"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Adjust
                    </Button>
                    <Button size="sm" onClick={() => handleReorder(item)} className="bg-blue-600 hover:bg-blue-700">
                      Reorder
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Stock Movements */}
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Recent Stock Movements</CardTitle>
              <Button variant="ghost" size="sm">
                <Eye className="h-4 w-4 mr-1" />
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {/* Search and Filters */}
            <div className="flex items-center space-x-3 mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search movements..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                <option value="sale">Sales</option>
                <option value="restock">Restocks</option>
                <option value="adjustment">Adjustments</option>
              </select>
            </div>

            {/* Movements List */}
            <div className="space-y-3">
              {filteredMovements.map((movement) => (
                <div key={movement.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-white rounded-full">
                      {getMovementIcon(movement.type)}
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 text-sm">{movement.productName}</h3>
                      <p className="text-xs text-gray-500">
                        {movement.sku} • {movement.timestamp} • {movement.user}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-500">{movement.previousStock} →</span>
                      <span className="font-medium text-gray-900 text-sm">{movement.newStock}</span>
                      <span className={`text-sm font-medium ${getMovementColor(movement.type)}`}>
                        ({movement.quantity > 0 ? "+" : ""}
                        {movement.quantity})
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Ref: {movement.reference}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Stock Adjustment Modal */}
        {showAdjustModal && selectedProduct && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <Card className="w-full max-w-md">
              <CardHeader>
                <CardTitle>Adjust Stock Level</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="font-medium text-gray-900">{selectedProduct.name}</p>
                  <p className="text-sm text-gray-500">Current Stock: {selectedProduct.currentStock}</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="adjustment">Adjustment Quantity</Label>
                  <Input id="adjustment" type="number" placeholder="Enter quantity (+/-)" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reason">Reason</Label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">Select reason</option>
                    <option value="damaged">Damaged goods</option>
                    <option value="expired">Expired items</option>
                    <option value="theft">Theft/Loss</option>
                    <option value="recount">Stock recount</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div className="flex space-x-3">
                  <Button variant="outline" className="flex-1 bg-transparent" onClick={() => setShowAdjustModal(false)}>
                    Cancel
                  </Button>
                  <Button
                    className="flex-1 bg-blue-600 hover:bg-blue-700"
                    onClick={() => {
                      setShowAdjustModal(false)
                      alert("Stock adjustment recorded")
                    }}
                  >
                    Apply Adjustment
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
