"use client"

import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useCallback, useEffect, useState } from "react"
import { Id } from "@/convex/_generated/dataModel"
import { toast } from "sonner"

interface UseConvexCartReturn {
  cartItems: any[]
  selectedItems: any[]
  totalPrice: number
  loading: boolean
  error: Error | null
  updateQuantity: (id: Id<"cart">, quantity: number) => Promise<void>
  toggleSelect: (id: Id<"cart">, selected: boolean) => Promise<void>
  removeItem: (id: Id<"cart">) => Promise<void>
  clearCart: () => Promise<void>
}

export function useConvexCart(userId: string): UseConvexCartReturn {
  const [error, setError] = useState<Error | null>(null)
  const cartItems = useQuery(api.cart.list, { userId })
  
  // Mutations
  const updateQuantityMutation = useMutation(api.cart.updateQuantity)
  const toggleSelectMutation = useMutation(api.cart.toggleSelect)
  const removeItemMutation = useMutation(api.cart.remove)
  const clearCartMutation = useMutation(api.cart.clear)
  
  // Memoized derived state
  const selectedItems = (cartItems || []).filter(item => item.selected)
  
  const totalPrice = (cartItems || []).reduce((sum, item) => {
    if (item.selected && item.product) {
      return sum + (item.quantity * (item.product.price || 0))
    }
    return sum
  }, 0)
  
  // Action handlers with improved error handling
  const updateQuantity = useCallback(async (id: Id<"cart">, quantity: number) => {
    try {
      await updateQuantityMutation({ id, quantity })
      setError(null)
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to update quantity')
      setError(error)
      toast.error(error.message)
      throw error
    }
  }, [updateQuantityMutation])
  
  const toggleSelect = useCallback(async (id: Id<"cart">, selected: boolean) => {
    try {
      await toggleSelectMutation({ id, selected })
      setError(null)
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to toggle selection')
      setError(error)
      toast.error(error.message)
      throw error
    }
  }, [toggleSelectMutation])

  const removeItem = useCallback(async (id: Id<"cart">) => {
    try {
      await removeItemMutation({ id })
      setError(null)
      toast.success("Item removed from cart")
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to remove item')
      setError(error)
      toast.error(error.message)
      throw error
    }
  }, [removeItemMutation])

  const clearCart = useCallback(async () => {
    try {
      await clearCartMutation({ userId })
      setError(null)
      toast.success("Cart cleared")
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to clear cart')
      setError(error)
      toast.error(error.message)
      throw error
    }
  }, [clearCartMutation, userId])
  
  // Reset error state when cart items change
  useEffect(() => {
    if (cartItems) {
      setError(null)
    }
  }, [cartItems])
  
  return {
    cartItems: cartItems || [],
    selectedItems,
    totalPrice,
    loading: cartItems === undefined,
    error,
    updateQuantity,
    toggleSelect,
    removeItem,
    clearCart
  }
}


