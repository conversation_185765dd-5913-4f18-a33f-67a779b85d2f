"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { 
  PaymentMethod, 
  PAYMENT_METHODS, 
  calculatePaymentFees, 
  formatCurrency,
  isPaymentMethodAvailable 
} from "@/lib/payment"
import { CreditCard, Smartphone, Building2, Banknote, Gift } from "lucide-react"

interface PaymentMethodsProps {
  amount: number
  selectedMethod: PaymentMethod | null
  onMethodSelect: (method: PaymentMethod) => void
  onPaymentDetailsChange: (details: any) => void
  disabled?: boolean
}

export function PaymentMethods({
  amount,
  selectedMethod,
  onMethodSelect,
  onPaymentDetailsChange,
  disabled = false
}: PaymentMethodsProps) {
  const [cardDetails, setCardDetails] = useState({
    number: "",
    expiry: "",
    cvv: "",
    name: ""
  })
  
  const [mobileDetails, setMobileDetails] = useState({
    phoneNumber: "",
    provider: "mtn"
  })

  const [eftDetails, setEftDetails] = useState({
    accountHolder: "",
    bankName: ""
  })

  const getMethodIcon = (method: PaymentMethod) => {
    switch (method) {
      case "card":
        return <CreditCard className="w-5 h-5" />
      case "mobile_money":
        return <Smartphone className="w-5 h-5" />
      case "eft":
        return <Building2 className="w-5 h-5" />
      case "cash_on_delivery":
        return <Banknote className="w-5 h-5" />
      case "store_credit":
        return <Gift className="w-5 h-5" />
      default:
        return null
    }
  }

  const handleCardDetailsChange = (field: string, value: string) => {
    const newDetails = { ...cardDetails, [field]: value }
    setCardDetails(newDetails)
    onPaymentDetailsChange({ card: newDetails })
  }

  const handleMobileDetailsChange = (field: string, value: string) => {
    const newDetails = { ...mobileDetails, [field]: value }
    setMobileDetails(newDetails)
    onPaymentDetailsChange({ mobile: newDetails })
  }

  const handleEftDetailsChange = (field: string, value: string) => {
    const newDetails = { ...eftDetails, [field]: value }
    setEftDetails(newDetails)
    onPaymentDetailsChange({ eft: newDetails })
  }

  const availableMethods = Object.entries(PAYMENT_METHODS).filter(
    ([_, config]) => isPaymentMethodAvailable(config.id as PaymentMethod)
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment Method</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <RadioGroup
          value={selectedMethod || ""}
          onValueChange={(value) => onMethodSelect(value as PaymentMethod)}
          disabled={disabled}
        >
          {availableMethods.map(([methodId, config]) => {
            const method = methodId as PaymentMethod
            const fees = calculatePaymentFees(amount, method)
            const totalAmount = amount + fees

            return (
              <div key={method} className="space-y-3">
                <div className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                  <RadioGroupItem value={method} id={method} />
                  <Label htmlFor={method} className="flex-1 cursor-pointer">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getMethodIcon(method)}
                        <div>
                          <div className="font-medium">{config.name}</div>
                          <div className="text-sm text-gray-500">{config.description}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        {fees > 0 && (
                          <div className="text-sm text-gray-500">
                            Fee: {formatCurrency(fees)}
                          </div>
                        )}
                        <div className="font-medium">
                          Total: {formatCurrency(totalAmount)}
                        </div>
                      </div>
                    </div>
                  </Label>
                </div>

                {/* Payment method specific forms */}
                {selectedMethod === method && (
                  <div className="ml-6 p-4 bg-gray-50 rounded-lg space-y-4">
                    {method === "card" && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="col-span-2">
                          <Label htmlFor="cardNumber">Card Number</Label>
                          <Input
                            id="cardNumber"
                            placeholder="1234 5678 9012 3456"
                            value={cardDetails.number}
                            onChange={(e) => handleCardDetailsChange("number", e.target.value)}
                            disabled={disabled}
                          />
                        </div>
                        <div className="col-span-2">
                          <Label htmlFor="cardName">Cardholder Name</Label>
                          <Input
                            id="cardName"
                            placeholder="John Doe"
                            value={cardDetails.name}
                            onChange={(e) => handleCardDetailsChange("name", e.target.value)}
                            disabled={disabled}
                          />
                        </div>
                        <div>
                          <Label htmlFor="cardExpiry">Expiry Date</Label>
                          <Input
                            id="cardExpiry"
                            placeholder="MM/YY"
                            value={cardDetails.expiry}
                            onChange={(e) => handleCardDetailsChange("expiry", e.target.value)}
                            disabled={disabled}
                          />
                        </div>
                        <div>
                          <Label htmlFor="cardCvv">CVV</Label>
                          <Input
                            id="cardCvv"
                            placeholder="123"
                            value={cardDetails.cvv}
                            onChange={(e) => handleCardDetailsChange("cvv", e.target.value)}
                            disabled={disabled}
                          />
                        </div>
                      </div>
                    )}

                    {method === "mobile_money" && (
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="mobileProvider">Provider</Label>
                          <RadioGroup
                            value={mobileDetails.provider}
                            onValueChange={(value) => handleMobileDetailsChange("provider", value)}
                            className="flex gap-4 mt-2"
                            disabled={disabled}
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="mtn" id="mtn" />
                              <Label htmlFor="mtn">MTN Mobile Money</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="vodacom" id="vodacom" />
                              <Label htmlFor="vodacom">Vodacom M-Pesa</Label>
                            </div>
                          </RadioGroup>
                        </div>
                        <div>
                          <Label htmlFor="mobileNumber">Mobile Number</Label>
                          <Input
                            id="mobileNumber"
                            placeholder="0XX XXX XXXX"
                            value={mobileDetails.phoneNumber}
                            onChange={(e) => handleMobileDetailsChange("phoneNumber", e.target.value)}
                            disabled={disabled}
                          />
                        </div>
                      </div>
                    )}

                    {method === "eft" && (
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="accountHolder">Account Holder Name</Label>
                          <Input
                            id="accountHolder"
                            placeholder="John Doe"
                            value={eftDetails.accountHolder}
                            onChange={(e) => handleEftDetailsChange("accountHolder", e.target.value)}
                            disabled={disabled}
                          />
                        </div>
                        <div>
                          <Label htmlFor="bankName">Bank Name</Label>
                          <Input
                            id="bankName"
                            placeholder="Standard Bank"
                            value={eftDetails.bankName}
                            onChange={(e) => handleEftDetailsChange("bankName", e.target.value)}
                            disabled={disabled}
                          />
                        </div>
                        <div className="p-3 bg-blue-50 border border-blue-200 rounded">
                          <p className="text-sm text-blue-800">
                            You will receive banking details to complete the EFT transfer after confirming your order.
                          </p>
                        </div>
                      </div>
                    )}

                    {method === "cash_on_delivery" && (
                      <div className="p-3 bg-green-50 border border-green-200 rounded">
                        <p className="text-sm text-green-800">
                          Pay with cash when your order is delivered. Please have exact change ready.
                        </p>
                      </div>
                    )}

                    {method === "store_credit" && (
                      <div className="p-3 bg-purple-50 border border-purple-200 rounded">
                        <p className="text-sm text-purple-800">
                          Your store credit will be used for this purchase. Current balance: {formatCurrency(1250)}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )
          })}
        </RadioGroup>

        {selectedMethod && (
          <div className="pt-4 border-t">
            <div className="flex justify-between items-center text-lg font-semibold">
              <span>Total Amount:</span>
              <span>{formatCurrency(amount + calculatePaymentFees(amount, selectedMethod))}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
