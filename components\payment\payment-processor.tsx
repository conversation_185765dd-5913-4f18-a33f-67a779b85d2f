"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { 
  PaymentMethod, 
  PaymentDetails, 
  PaymentResult,
  processPayment,
  formatCurrency,
  generatePaymentReference
} from "@/lib/payment"
import { LoadingSpinner } from "@/components/ui/loading"
import { CheckCircle, XCircle, Clock, AlertTriangle } from "lucide-react"
import { toast } from "sonner"

interface PaymentProcessorProps {
  amount: number
  method: PaymentMethod
  paymentDetails: any
  orderData: any
  onPaymentSuccess: (result: PaymentResult) => void
  onPaymentError: (error: string) => void
  disabled?: boolean
}

export function PaymentProcessor({
  amount,
  method,
  paymentDetails,
  orderData,
  onPaymentSuccess,
  onPaymentError,
  disabled = false
}: PaymentProcessorProps) {
  const [processing, setProcessing] = useState(false)
  const [result, setResult] = useState<PaymentResult | null>(null)

  const handlePayment = async () => {
    setProcessing(true)
    setResult(null)

    try {
      const details: PaymentDetails = {
        method,
        amount,
        currency: "ZAR",
        reference: generatePaymentReference(),
        metadata: {
          orderId: orderData.id,
          userId: orderData.userId,
          items: orderData.items,
          paymentDetails
        }
      }

      const paymentResult = await processPayment(details)
      setResult(paymentResult)

      if (paymentResult.success) {
        toast.success("Payment processed successfully!")
        onPaymentSuccess(paymentResult)
      } else {
        toast.error(paymentResult.error || "Payment failed")
        onPaymentError(paymentResult.error || "Payment failed")
      }
    } catch (error) {
      const errorMessage = "Payment processing failed. Please try again."
      toast.error(errorMessage)
      onPaymentError(errorMessage)
      setResult({
        success: false,
        error: errorMessage
      })
    } finally {
      setProcessing(false)
    }
  }

  const getStatusIcon = (success: boolean) => {
    if (success) {
      return <CheckCircle className="w-5 h-5 text-green-500" />
    } else {
      return <XCircle className="w-5 h-5 text-red-500" />
    }
  }

  const getMethodDisplayName = (method: PaymentMethod) => {
    switch (method) {
      case "card":
        return "Credit/Debit Card"
      case "mobile_money":
        return "Mobile Money"
      case "eft":
        return "EFT/Bank Transfer"
      case "cash_on_delivery":
        return "Cash on Delivery"
      case "store_credit":
        return "Store Credit"
      default:
        return method
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {processing && <LoadingSpinner size="sm" />}
          Payment Processing
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Payment Summary */}
        <div className="p-4 bg-gray-50 rounded-lg space-y-2">
          <div className="flex justify-between">
            <span className="text-sm text-gray-600">Payment Method:</span>
            <span className="font-medium">{getMethodDisplayName(method)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-600">Amount:</span>
            <span className="font-medium">{formatCurrency(amount)}</span>
          </div>
          {orderData.reference && (
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Order Reference:</span>
              <span className="font-mono text-sm">{orderData.reference}</span>
            </div>
          )}
        </div>

        {/* Payment Status */}
        {result && (
          <Alert className={result.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
            <div className="flex items-center gap-2">
              {getStatusIcon(result.success)}
              <AlertDescription className={result.success ? "text-green-800" : "text-red-800"}>
                {result.message || result.error}
              </AlertDescription>
            </div>
            {result.success && result.reference && (
              <div className="mt-2 p-2 bg-white rounded border">
                <div className="text-sm">
                  <strong>Payment Reference:</strong> {result.reference}
                </div>
                {result.paymentId && (
                  <div className="text-sm">
                    <strong>Payment ID:</strong> {result.paymentId}
                  </div>
                )}
              </div>
            )}
          </Alert>
        )}

        {/* Processing Status */}
        {processing && (
          <Alert className="border-blue-200 bg-blue-50">
            <Clock className="w-4 h-4" />
            <AlertDescription className="text-blue-800">
              Processing your payment... Please do not close this window.
            </AlertDescription>
          </Alert>
        )}

        {/* Payment Instructions for specific methods */}
        {method === "eft" && !processing && !result && (
          <Alert className="border-blue-200 bg-blue-50">
            <AlertTriangle className="w-4 h-4" />
            <AlertDescription className="text-blue-800">
              After confirming your order, you will receive banking details to complete the EFT transfer.
              Your order will be processed once payment is received.
            </AlertDescription>
          </Alert>
        )}

        {method === "cash_on_delivery" && !processing && !result && (
          <Alert className="border-green-200 bg-green-50">
            <AlertTriangle className="w-4 h-4" />
            <AlertDescription className="text-green-800">
              Your order will be prepared for delivery. Please have exact change ready when the delivery arrives.
            </AlertDescription>
          </Alert>
        )}

        {method === "mobile_money" && !processing && !result && (
          <Alert className="border-purple-200 bg-purple-50">
            <AlertTriangle className="w-4 h-4" />
            <AlertDescription className="text-purple-800">
              You will receive a payment prompt on your mobile device. Please approve the transaction to complete your order.
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3">
          {!result && (
            <Button
              onClick={handlePayment}
              disabled={processing || disabled}
              className="flex-1"
              size="lg"
            >
              {processing ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Processing...
                </>
              ) : (
                `Pay ${formatCurrency(amount)}`
              )}
            </Button>
          )}

          {result && !result.success && (
            <Button
              onClick={handlePayment}
              disabled={processing || disabled}
              variant="outline"
              className="flex-1"
            >
              Try Again
            </Button>
          )}

          {result && result.success && (
            <Button
              onClick={() => onPaymentSuccess(result)}
              className="flex-1"
              size="lg"
            >
              Continue to Order Confirmation
            </Button>
          )}
        </div>

        {/* Security Notice */}
        <div className="text-xs text-gray-500 text-center">
          🔒 Your payment information is secure and encrypted
        </div>
      </CardContent>
    </Card>
  )
}
