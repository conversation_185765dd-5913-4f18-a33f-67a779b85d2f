"use client"

import { useEffect, useState } from "react"
import { <PERSON>Circle, ShoppingCart, ArrowRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"

export default function WelcomePage() {
  const router = useRouter()
  const [step, setStep] = useState(0)
  const createUser = useMutation(api.users.register)

  useEffect(() => {
    const timer = setTimeout(() => {
      if (step < 3) {
        setStep(step + 1)
      }
    }, 1000)

    return () => clearTimeout(timer)
  }, [step])

  const handleGetStarted = async () => {
    // Get user data from local storage or context
    const userData = JSON.parse(localStorage.getItem('signupData') || '{}');
    
    try {
      await createUser({
        email: userData.email || '',
        name: `${userData.firstName} ${userData.lastName}`.trim(),
        password: userData.password || 'defaultPassword123',
        phone: userData.phone
      });
      
      // Clear the signup data
      localStorage.removeItem('signupData');
      router.push("/home");
    } catch (error) {
      console.error('Failed to create user:', error);
      // Handle error (show toast, etc.)
    }
  }

  const steps = [
    "Account created successfully",
    "Setting up your profile",
    "Preparing your shopping experience",
    "Welcome to Spaza Smart Order!",
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex flex-col items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
        <div className="mb-6">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-10 h-10 text-green-600" />
          </div>
          <div className="flex items-center justify-center gap-2 mb-2">
            <ShoppingCart className="w-6 h-6 text-blue-600" />
            <h1 className="text-xl font-bold text-gray-900">Spaza Smart Order</h1>
          </div>
        </div>

        <div className="space-y-4 mb-8">
          {steps.map((stepText, index) => (
            <div
              key={index}
              className={`flex items-center gap-3 p-3 rounded-lg transition-all duration-500 ${
                index <= step ? "bg-green-50 text-green-800" : "bg-gray-50 text-gray-400"
              }`}
            >
              <div
                className={`w-6 h-6 rounded-full flex items-center justify-center transition-all duration-500 ${
                  index <= step ? "bg-green-600 text-white" : "bg-gray-300 text-gray-500"
                }`}
              >
                {index <= step ? (
                  <CheckCircle className="w-4 h-4" />
                ) : (
                  <span className="text-xs font-bold">{index + 1}</span>
                )}
              </div>
              <span className="text-sm font-medium">{stepText}</span>
            </div>
          ))}
        </div>

        {step >= 3 && (
          <div className="space-y-4 animate-fade-in">
            <h2 className="text-2xl font-bold text-gray-900">You're all set!</h2>
            <p className="text-gray-600">
              Start exploring fresh groceries from your local spaza shops and enjoy convenient delivery to your
              doorstep.
            </p>
            <Button
              onClick={handleGetStarted}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg font-medium"
            >
              Start Shopping
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
function createUser(arg0: { email: any; name: string; phone: any }) {
    throw new Error("Function not implemented.")
}

