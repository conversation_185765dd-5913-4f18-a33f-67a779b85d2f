"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON>Left } from "lucide-react"
import { Button } from "@/components/ui/button"
import { CheckoutForm } from "@/components/customer/checkout-form"
import { PaymentMethods } from "@/components/payment/payment-methods"
import { PaymentProcessor } from "@/components/payment/payment-processor"
import { useAuth } from "@/contexts/auth-context"
import { useConvexCart } from "@/hooks/use-convex-cart"
import { toast } from "sonner"
import { Skeleton } from "@/components/ui/skeleton"
import { PaymentMethod, PaymentResult, calculatePaymentFees } from "@/lib/payment"
import { useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"

export default function CheckoutPage() {
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()
  const [orderPlaced, setOrderPlaced] = useState(false)
  const [orderId, setOrderId] = useState<string | null>(null)
  const [currentStep, setCurrentStep] = useState<"details" | "payment" | "processing">("details")
  const [orderDetails, setOrderDetails] = useState<any>(null)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null)
  const [paymentDetails, setPaymentDetails] = useState<any>({})

  const { cartItems, totalPrice, loading: cartLoading } = useConvexCart(user?._id || "")
  const createOrder = useMutation(api.orders.create)
  const createPayment = useMutation(api.payments.createPayment)

  useEffect(() => {
    if (!authLoading && !user) {
      router.push("/login?redirect=/checkout")
      toast.info("Please sign in to proceed to checkout")
    }
  }, [user, authLoading, router])

  const handleOrderDetailsComplete = (details: any) => {
    setOrderDetails(details)
    setCurrentStep("payment")
  }

  const handlePaymentSuccess = async (result: PaymentResult) => {
    try {
      if (orderDetails && selectedPaymentMethod) {
        // Create the order with payment information
        const fees = calculatePaymentFees(totalPrice, selectedPaymentMethod)
        const total = totalPrice + fees

        const order = await createOrder({
          userId: user!._id,
          items: cartItems.map(item => ({
            productId: item.productId,
            name: item.product?.name || "",
            price: item.product?.price || 0,
            quantity: item.quantity,
            image: item.product?.image || "",
          })),
          subtotal: totalPrice,
          deliveryFee: orderDetails.deliveryFee || 0,
          paymentFees: fees,
          total,
          deliveryAddress: orderDetails.address,
          deliveryOption: orderDetails.deliveryOption,
          notes: orderDetails.notes,
          paymentMethod: selectedPaymentMethod,
          paymentReference: result.reference,
          paymentId: result.paymentId,
        })

        setOrderId(order)
        setOrderPlaced(true)
        toast.success("Order placed successfully!")

        // Redirect to order tracking page after 3 seconds
        setTimeout(() => {
          router.push(`/orders/${order}`)
        }, 3000)
      }
    } catch (error) {
      console.error("Failed to create order:", error)
      toast.error("Failed to create order. Please contact support.")
    }
  }

  const handlePaymentError = (error: string) => {
    toast.error(error)
    setCurrentStep("payment")
  }

  if (authLoading || !user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="space-y-4 w-full max-w-md p-4">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      </div>
    )
  }

  if (orderPlaced) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg p-8 text-center max-w-sm w-full shadow-lg">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-3">Order Confirmed!</h2>
          <p className="text-gray-600 mb-2">Your order #{orderId?.substring(0, 8).toUpperCase()} has been received.</p>
          <p className="text-sm text-gray-500 mb-6">We'll send you a confirmation email with details.</p>
          
          <div className="bg-blue-50 p-4 rounded-lg text-left text-sm text-blue-800 mb-6">
            <p className="font-medium mb-1">What's next?</p>
            <ul className="list-disc pl-5 space-y-1">
              <li>Track your order in real-time</li>
              <li>Receive SMS updates on your order status</li>
              <li>Estimated delivery time: 30-45 minutes</li>
            </ul>
          </div>
          
          <div className="flex flex-col space-y-3">
            <Button 
              onClick={() => router.push(`/track-order/${orderId}`)}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              Track Order
            </Button>
            <Button 
              variant="outline"
              onClick={() => router.push('/')}
              className="w-full"
            >
              Continue Shopping
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4 flex items-center gap-3 sticky top-0 z-10">
        <Button
          variant="ghost"
          size="sm"
          className="text-white hover:bg-blue-700 p-2"
          onClick={() => {
            if (currentStep === "payment") {
              setCurrentStep("details")
            } else if (currentStep === "processing") {
              setCurrentStep("payment")
            } else {
              router.back()
            }
          }}
        >
          <ArrowLeft className="w-5 h-5" />
        </Button>
        <h1 className="text-lg font-semibold">
          {currentStep === "details" ? "Checkout" :
           currentStep === "payment" ? "Payment" : "Processing"}
        </h1>
      </div>

      {/* Progress Steps */}
      <div className="bg-white border-b p-4">
        <div className="flex items-center justify-center space-x-4">
          <div className={`flex items-center ${currentStep === "details" ? "text-blue-600" : "text-gray-400"}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep === "details" ? "bg-blue-600 text-white" : "bg-gray-200"
            }`}>
              1
            </div>
            <span className="ml-2 text-sm font-medium">Details</span>
          </div>
          <div className="w-8 h-px bg-gray-300"></div>
          <div className={`flex items-center ${currentStep === "payment" ? "text-blue-600" : "text-gray-400"}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep === "payment" ? "bg-blue-600 text-white" : "bg-gray-200"
            }`}>
              2
            </div>
            <span className="ml-2 text-sm font-medium">Payment</span>
          </div>
          <div className="w-8 h-px bg-gray-300"></div>
          <div className={`flex items-center ${currentStep === "processing" ? "text-blue-600" : "text-gray-400"}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep === "processing" ? "bg-blue-600 text-white" : "bg-gray-200"
            }`}>
              3
            </div>
            <span className="ml-2 text-sm font-medium">Confirm</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 max-w-2xl mx-auto">
        {currentStep === "details" && (
          <CheckoutForm
            userId={user._id}
            onOrderComplete={handleOrderDetailsComplete}
          />
        )}

        {currentStep === "payment" && orderDetails && (
          <div className="space-y-6">
            <PaymentMethods
              amount={totalPrice + (orderDetails.deliveryFee || 0)}
              selectedMethod={selectedPaymentMethod}
              onMethodSelect={setSelectedPaymentMethod}
              onPaymentDetailsChange={setPaymentDetails}
            />

            {selectedPaymentMethod && (
              <Button
                onClick={() => setCurrentStep("processing")}
                className="w-full"
                size="lg"
              >
                Continue to Payment
              </Button>
            )}
          </div>
        )}

        {currentStep === "processing" && orderDetails && selectedPaymentMethod && (
          <PaymentProcessor
            amount={totalPrice + (orderDetails.deliveryFee || 0)}
            method={selectedPaymentMethod}
            paymentDetails={paymentDetails}
            orderData={{
              id: "temp",
              userId: user._id,
              items: cartItems,
              reference: `ORD-${Date.now()}`,
              ...orderDetails
            }}
            onPaymentSuccess={handlePaymentSuccess}
            onPaymentError={handlePaymentError}
          />
        )}
      </div>
    </div>
  )
}
