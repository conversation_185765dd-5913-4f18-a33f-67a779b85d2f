"use client"

import { useRouter, usePathname } from "next/navigation"
import { ShoppingCart, Package, User, Home } from "lucide-react"

interface BottomNavigationProps {
  cartCount?: number
}

export default function BottomNavigation({ cartCount = 0 }: BottomNavigationProps) {
  const router = useRouter()
  const pathname = usePathname()

  const navigationItems = [
    {
      id: "home",
      label: "Home",
      icon: Home,
      path: "/home",
      emoji: "🏠",
    },
    {
      id: "cart",
      label: "Cart",
      icon: ShoppingCart,
      path: "/",
      showBadge: true,
    },
    {
      id: "orders",
      label: "Orders",
      icon: Package,
      path: "/orders",
      emoji: "📦",
    },
    {
      id: "profile",
      label: "Profile",
      icon: User,
      path: "/profile",
      emoji: "👤",
    },
  ]

  const isActive = (path: string) => {
    if (path === "/" && pathname === "/") return true
    if (path !== "/" && pathname.startsWith(path)) return true
    return false
  }

  const handleNavigation = (path: string) => {
    router.push(path)
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
      <div className="grid grid-cols-4">
        {navigationItems.map((item) => {
          const Icon = item.icon
          const active = isActive(item.path)

          return (
            <button
              key={item.id}
              className={`flex flex-col items-center py-3 transition-colors ${
                active ? "text-blue-600" : "text-gray-600"
              }`}
              onClick={() => handleNavigation(item.path)}
            >
              {item.emoji ? (
                <div className="w-6 h-6 mb-1 text-2xl">{item.emoji}</div>
              ) : (
                <div className="relative">
                  <Icon className="w-6 h-6 mb-1" />
                  {item.showBadge && cartCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                      {cartCount}
                    </span>
                  )}
                </div>
              )}
              <span className="text-xs">{item.label}</span>
            </button>
          )
        })}
      </div>
    </div>
  )
}
