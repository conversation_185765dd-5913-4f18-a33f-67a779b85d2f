"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Package, Truck, CheckCircle, XCircle, Clock, MapPin, Phone, RotateCcw } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import BottomNavigation from "@/components/bottom-navigation"
import { useConvexOrders } from "@/hooks/use-convex-orders"
import { useConvexCart } from "@/hooks/use-convex-cart"

export default function OrdersPage() {
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const router = useRouter()
  
  // Get real-time data
  const { orders, loading, error } = useConvexOrders("demo-user", selectedStatus === "all" ? undefined : selectedStatus)
  const { cartItems } = useConvexCart("demo-user")
  const cartCount = cartItems.length

  // Calculate status counts from real-time data
  const statusFilters = [
    { id: "all", name: "All", count: orders?.length || 0 },
    { id: "pending", name: "Pending", count: orders?.filter(o => o.status === "pending").length || 0 },
    { id: "processing", name: "Processing", count: orders?.filter(o => o.status === "processing").length || 0 },
    { id: "delivered", name: "Delivered", count: orders?.filter(o => o.status === "delivered").length || 0 },
    { id: "cancelled", name: "Cancelled", count: orders?.filter(o => o.status === "cancelled").length || 0 },
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="w-4 h-4 text-yellow-500" />
      case "processing":
        return <Package className="w-4 h-4 text-orange-500" />
      case "delivered":
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case "cancelled":
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return <Clock className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      case "processing":
        return "bg-orange-100 text-orange-800"
      case "delivered":
        return "bg-green-100 text-green-800"
      case "cancelled":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "Pending"
      case "processing":
        return "Processing"
      case "delivered":
        return "Delivered"
      case "cancelled":
        return "Cancelled"
      default:
        return "Unknown"
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center text-red-600">
          Error loading orders: {error}
        </div>
      </div>
    )
  }

  const reorderItems = (orderId: string) => {
    router.push(`/reorder/${orderId}`)
  }

  const trackOrder = (orderId: string) => {
    router.push(`/track-order/${orderId}`)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-lg font-semibold">My Orders</h1>
            <p className="text-blue-100 text-sm">Track your order history</p>
          </div>
        </div>
      </div>

      {/* Status Filters */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex gap-2 overflow-x-auto pb-2">
          {statusFilters.map((filter) => (
            <Button
              key={filter.id}
              variant={selectedStatus === filter.id ? "default" : "outline"}
              size="sm"
              className={`flex-shrink-0 ${
                selectedStatus === filter.id ? "bg-blue-600 text-white" : "bg-white text-gray-700 border-gray-300"
              }`}
              onClick={() => setSelectedStatus(filter.id)}
            >
              {filter.name}
              <Badge variant="secondary" className="ml-2 bg-gray-100 text-gray-600">
                {filter.count}
              </Badge>
            </Button>
          ))}
        </div>
      </div>

      {/* Orders List */}
      <div className="p-4 space-y-4">
        {!orders || orders.length === 0 ? (
          <div className="text-center py-12">
            <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
            <p className="text-gray-500 mb-4">You haven't placed any orders yet.</p>
            <Button 
              className="bg-blue-600 hover:bg-blue-700 text-white"
              onClick={() => router.push("/home")}
            >
              Start Shopping
            </Button>
          </div>
        ) : (
          orders.map((order) => (
            <div key={order._id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              {/* Order Header */}
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h3 className="font-semibold text-gray-900">{order.orderNumber}</h3>
                  <p className="text-sm text-gray-500">
                    {new Date(order.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <Badge className={`${getStatusColor(order.status)} border-0`}>
                  <div className="flex items-center gap-1">
                    {getStatusIcon(order.status)}
                    {getStatusText(order.status)}
                  </div>
                </Badge>
              </div>

              {/* Order Items */}
              <div className="space-y-2 mb-3">
                {order.items.map((item, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <img
                      src={item.image || "/placeholder.svg"}
                      alt={item.name}
                      className="w-12 h-12 object-cover rounded-md"
                    />
                    <div className="flex-1">
                      <p className="font-medium text-sm text-gray-900">{item.name}</p>
                      <p className="text-xs text-gray-500">
                        Qty: {item.quantity} × R {item.price.toFixed(2)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Delivery Info */}
              <div className="bg-gray-50 rounded-lg p-3 mb-3">
                <div className="flex items-start gap-2">
                  <MapPin className="w-4 h-4 text-gray-500 mt-0.5" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">Delivery Address</p>
                    <p className="text-sm text-gray-600">{order.deliveryAddress}</p>
                    {order.estimatedDelivery && (
                      <p className="text-sm text-blue-600 mt-1">
                        <Clock className="w-3 h-3 inline mr-1" />
                        {order.estimatedDelivery}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Order Total */}
              <div className="flex items-center justify-between mb-3">
                <span className="font-medium text-gray-900">Total</span>
                <span className="font-semibold text-lg text-blue-600">R {order.total.toFixed(2)}</span>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                {order.status === "delivered" && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 bg-transparent"
                    onClick={() => reorderItems(order._id)}
                  >
                    <RotateCcw className="w-4 h-4 mr-1" />
                    Reorder
                  </Button>
                )}
                {["processing"].includes(order.status) && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 bg-transparent"
                    onClick={() => trackOrder(order._id)}
                  >
                    <Truck className="w-4 h-4 mr-1" />
                    Track Order
                  </Button>
                )}
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1 bg-transparent"
                  onClick={() => router.push("/support")}
                >
                  <Phone className="w-4 h-4 mr-1" />
                  Support
                </Button>
              </div>
            </div>
          ))
        )}
      </div>

      <BottomNavigation cartCount={cartCount} />

      {/* Bottom padding to account for fixed navigation */}
      <div className="h-20"></div>
    </div>
  )
}
