"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, MapPin, Users, Calendar } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function DeliveryManagementPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("zones")

  const deliveryZones = [
    {
      id: "zone-1",
      name: "City Center",
      areas: ["CBD", "Braamfontein", "Newtown"],
      deliveryFee: 25,
      deliveryTime: "30-45 mins",
      status: "active",
      drivers: 8,
    },
    {
      id: "zone-2",
      name: "Northern Suburbs",
      areas: ["Sandton", "Rosebank", "Randburg"],
      deliveryFee: 35,
      deliveryTime: "45-60 mins",
      status: "active",
      drivers: 12,
    },
    {
      id: "zone-3",
      name: "Southern Suburbs",
      areas: ["Soweto", "Lenasia", "Orange Farm"],
      deliveryFee: 40,
      deliveryTime: "60-75 mins",
      status: "active",
      drivers: 6,
    },
    {
      id: "zone-4",
      name: "Eastern Areas",
      areas: ["Alexandra", "Tembisa", "Kempton Park"],
      deliveryFee: 45,
      deliveryTime: "60-90 mins",
      status: "limited",
      drivers: 4,
    },
  ]

  const dropPoints = [
    {
      id: "dp-1",
      name: "Maponya Mall Collection Point",
      address: "Maponya Mall, Soweto",
      hours: "9:00 AM - 6:00 PM",
      fee: 15,
      capacity: "50 orders/day",
      status: "active",
    },
    {
      id: "dp-2",
      name: "Eastgate Shopping Centre",
      address: "Eastgate Shopping Centre, Bedfordview",
      hours: "10:00 AM - 8:00 PM",
      fee: 20,
      capacity: "75 orders/day",
      status: "active",
    },
    {
      id: "dp-3",
      name: "Community Center - Alexandra",
      address: "Alexandra Community Center",
      hours: "8:00 AM - 5:00 PM",
      fee: 10,
      capacity: "30 orders/day",
      status: "active",
    },
  ]

  const scheduledDeliveries = [
    {
      id: "sched-1",
      customerName: "Sarah Mthembu",
      frequency: "Weekly",
      nextDelivery: "2024-01-15",
      items: "Groceries Package",
      zone: "Southern Suburbs",
      status: "active",
    },
    {
      id: "sched-2",
      customerName: "John Ndaba",
      frequency: "Bi-weekly",
      nextDelivery: "2024-01-18",
      items: "Household Essentials",
      zone: "Northern Suburbs",
      status: "active",
    },
    {
      id: "sched-3",
      customerName: "Community Group - Tembisa",
      frequency: "Monthly",
      nextDelivery: "2024-01-20",
      items: "Bulk Order",
      zone: "Eastern Areas",
      status: "pending",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4 flex items-center gap-3">
        <Button variant="ghost" size="sm" className="text-white hover:bg-blue-700 p-2" onClick={() => router.back()}>
          <ArrowLeft className="w-5 h-5" />
        </Button>
        <h1 className="text-lg font-semibold">Delivery Management</h1>
      </div>

      <div className="p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="zones">Delivery Zones</TabsTrigger>
            <TabsTrigger value="droppoints">Drop Points</TabsTrigger>
            <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
          </TabsList>

          <TabsContent value="zones" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="w-5 h-5 text-blue-600" />
                  Delivery Zones & Pricing
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {deliveryZones.map((zone) => (
                  <div key={zone.id} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-semibold text-gray-900">{zone.name}</h3>
                        <p className="text-sm text-gray-600">{zone.areas.join(", ")}</p>
                      </div>
                      <Badge
                        variant="secondary"
                        className={
                          zone.status === "active"
                            ? "bg-green-100 text-green-800"
                            : zone.status === "limited"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                        }
                      >
                        {zone.status}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Delivery Fee</p>
                        <p className="font-semibold text-blue-600">R {zone.deliveryFee}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Delivery Time</p>
                        <p className="font-semibold">{zone.deliveryTime}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Active Drivers</p>
                        <p className="font-semibold">{zone.drivers} drivers</p>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Zone Coverage Map Placeholder */}
            <Card>
              <CardHeader>
                <CardTitle>Coverage Map</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Interactive delivery zone map</p>
                    <p className="text-sm text-gray-400">Real-time driver locations and zone boundaries</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="droppoints" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-blue-600" />
                  Community Drop Points
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {dropPoints.map((point) => (
                  <div key={point.id} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-semibold text-gray-900">{point.name}</h3>
                        <p className="text-sm text-gray-600">{point.address}</p>
                      </div>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        {point.status}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Collection Fee</p>
                        <p className="font-semibold text-blue-600">R {point.fee}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Operating Hours</p>
                        <p className="font-semibold">{point.hours}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Daily Capacity</p>
                        <p className="font-semibold">{point.capacity}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Benefits of Drop Points</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                    <p>Lower delivery costs for customers in remote areas</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                    <p>Convenient collection times that fit your schedule</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                    <p>Secure storage until collection</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                    <p>Community-based delivery solution</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="scheduled" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-blue-600" />
                  Scheduled Deliveries
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {scheduledDeliveries.map((delivery) => (
                  <div key={delivery.id} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-semibold text-gray-900">{delivery.customerName}</h3>
                        <p className="text-sm text-gray-600">{delivery.items}</p>
                      </div>
                      <Badge
                        variant="secondary"
                        className={
                          delivery.status === "active" ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"
                        }
                      >
                        {delivery.status}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Frequency</p>
                        <p className="font-semibold">{delivery.frequency}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Next Delivery</p>
                        <p className="font-semibold">{delivery.nextDelivery}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Zone</p>
                        <p className="font-semibold">{delivery.zone}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Schedule Your Regular Deliveries</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-gray-600">
                    Set up regular deliveries for your essential items and save time and money.
                  </p>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <h4 className="font-semibold text-blue-900">Weekly Deliveries</h4>
                      <p className="text-blue-700">5% discount on all items</p>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <h4 className="font-semibold text-green-900">Monthly Bulk Orders</h4>
                      <p className="text-green-700">10% discount + free delivery</p>
                    </div>
                  </div>
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                    <Calendar className="w-4 h-4 mr-2" />
                    Schedule New Delivery
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
