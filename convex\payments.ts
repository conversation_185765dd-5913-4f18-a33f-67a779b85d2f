import { v } from "convex/values"
import { mutation, query } from "./_generated/server"
import { Id } from "./_generated/dataModel"

// Create a payment record
export const createPayment = mutation({
  args: {
    orderId: v.id("orders"),
    method: v.union(
      v.literal("card"),
      v.literal("cash_on_delivery"),
      v.literal("eft"),
      v.literal("mobile_money"),
      v.literal("store_credit")
    ),
    amount: v.number(),
    reference: v.string(),
    paymentId: v.optional(v.string()),
    status: v.union(
      v.literal("pending"),
      v.literal("processing"),
      v.literal("completed"),
      v.literal("failed"),
      v.literal("refunded")
    ),
  },
  handler: async (ctx, args) => {
    // Update the order with payment information
    await ctx.db.patch(args.orderId, {
      paymentMethod: args.method,
      paymentReference: args.reference,
      paymentId: args.paymentId,
      paymentStatus: args.status,
      updatedAt: Date.now(),
    })

    return {
      success: true,
      orderId: args.orderId,
      paymentReference: args.reference,
    }
  },
})

// Update payment status
export const updatePaymentStatus = mutation({
  args: {
    orderId: v.id("orders"),
    status: v.union(
      v.literal("pending"),
      v.literal("processing"),
      v.literal("completed"),
      v.literal("failed"),
      v.literal("refunded")
    ),
    paymentId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const order = await ctx.db.get(args.orderId)
    if (!order) {
      throw new Error("Order not found")
    }

    await ctx.db.patch(args.orderId, {
      paymentStatus: args.status,
      paymentId: args.paymentId || order.paymentId,
      updatedAt: Date.now(),
    })

    // If payment is completed, update order status
    if (args.status === "completed") {
      await ctx.db.patch(args.orderId, {
        status: "confirmed",
        updatedAt: Date.now(),
      })
    }

    return {
      success: true,
      orderId: args.orderId,
      paymentStatus: args.status,
    }
  },
})

// Get payment status for an order
export const getPaymentStatus = query({
  args: {
    orderId: v.id("orders"),
  },
  handler: async (ctx, args) => {
    const order = await ctx.db.get(args.orderId)
    if (!order) {
      return null
    }

    return {
      orderId: args.orderId,
      paymentStatus: order.paymentStatus,
      paymentMethod: order.paymentMethod,
      paymentReference: order.paymentReference,
      paymentId: order.paymentId,
      amount: order.total,
    }
  },
})

// Process refund
export const processRefund = mutation({
  args: {
    orderId: v.id("orders"),
    amount: v.number(),
    reason: v.string(),
  },
  handler: async (ctx, args) => {
    const order = await ctx.db.get(args.orderId)
    if (!order) {
      throw new Error("Order not found")
    }

    if (order.paymentStatus !== "completed") {
      throw new Error("Cannot refund order that hasn't been paid")
    }

    // Update payment status to refunded
    await ctx.db.patch(args.orderId, {
      paymentStatus: "refunded",
      status: "cancelled",
      notes: order.notes ? `${order.notes}\nRefund: ${args.reason}` : `Refund: ${args.reason}`,
      updatedAt: Date.now(),
    })

    return {
      success: true,
      orderId: args.orderId,
      refundAmount: args.amount,
      reason: args.reason,
    }
  },
})

// Get orders by payment status
export const getOrdersByPaymentStatus = query({
  args: {
    status: v.union(
      v.literal("pending"),
      v.literal("processing"),
      v.literal("completed"),
      v.literal("failed"),
      v.literal("refunded")
    ),
  },
  handler: async (ctx, args) => {
    const orders = await ctx.db
      .query("orders")
      .withIndex("by_payment_status", (q) => q.eq("paymentStatus", args.status))
      .collect()

    return orders
  },
})

// Get orders by payment method
export const getOrdersByPaymentMethod = query({
  args: {
    method: v.union(
      v.literal("card"),
      v.literal("cash_on_delivery"),
      v.literal("eft"),
      v.literal("mobile_money"),
      v.literal("store_credit")
    ),
  },
  handler: async (ctx, args) => {
    const orders = await ctx.db
      .query("orders")
      .withIndex("by_payment_method", (q) => q.eq("paymentMethod", args.method))
      .collect()

    return orders
  },
})

// Verify payment (for webhook handling)
export const verifyPayment = mutation({
  args: {
    paymentReference: v.string(),
    paymentId: v.string(),
    status: v.union(v.literal("completed"), v.literal("failed")),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    // Find order by payment reference
    const orders = await ctx.db.query("orders").collect()
    const order = orders.find(o => o.paymentReference === args.paymentReference)
    
    if (!order) {
      throw new Error("Order not found for payment reference")
    }

    // Update payment status
    await ctx.db.patch(order._id, {
      paymentStatus: args.status,
      paymentId: args.paymentId,
      updatedAt: Date.now(),
    })

    // If payment completed, update order status
    if (args.status === "completed") {
      await ctx.db.patch(order._id, {
        status: "confirmed",
        updatedAt: Date.now(),
      })
    }

    return {
      success: true,
      orderId: order._id,
      orderNumber: order.orderNumber,
      paymentStatus: args.status,
    }
  },
})

// Get payment analytics
export const getPaymentAnalytics = query({
  args: {
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const startDate = args.startDate || (Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
    const endDate = args.endDate || Date.now()

    const orders = await ctx.db
      .query("orders")
      .withIndex("by_created_at")
      .filter((q) => 
        q.and(
          q.gte(q.field("createdAt"), startDate),
          q.lte(q.field("createdAt"), endDate)
        )
      )
      .collect()

    const analytics = {
      totalOrders: orders.length,
      totalRevenue: 0,
      paymentMethods: {} as Record<string, number>,
      paymentStatuses: {} as Record<string, number>,
      averageOrderValue: 0,
    }

    orders.forEach(order => {
      if (order.paymentStatus === "completed") {
        analytics.totalRevenue += order.total
      }

      analytics.paymentMethods[order.paymentMethod] = 
        (analytics.paymentMethods[order.paymentMethod] || 0) + 1

      analytics.paymentStatuses[order.paymentStatus] = 
        (analytics.paymentStatuses[order.paymentStatus] || 0) + 1
    })

    analytics.averageOrderValue = analytics.totalOrders > 0 
      ? analytics.totalRevenue / analytics.totalOrders 
      : 0

    return analytics
  },
})
