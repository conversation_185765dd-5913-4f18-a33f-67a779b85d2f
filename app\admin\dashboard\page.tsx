"use client"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent} from "@/components/ui/card"  
import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { Plus, Package, BarChart3, TrendingUp } from "lucide-react"
import { ConvexDashboard } from "@/components/admin/convex-dashboard"
import { ConvexNotifications } from "@/components/convex-notifications"

const DashboardPage = () => {
  const router = useRouter()

  return (
    <div className="space-y-6">
      {/* Header with Notifications */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Shop Dashboard</h1>
        <ConvexNotifications userId="shop-owner" isShopOwner={true} />
      </div>

      {/* Real-time Dashboard */}
      <ConvexDashboard shopOwnerId="shop-owner" />

      {/* Quick Actions */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-3">
            <Button className="h-12 bg-blue-600 hover:bg-blue-700" onClick={() => router.push("/admin/products/add")}>
              <Plus className="h-4 w-4 mr-2" />
              Add Product
            </Button>
            <Button variant="outline" className="h-12 bg-transparent" onClick={() => router.push("/admin/inventory")}>
              <Package className="h-4 w-4 mr-2" />
              Manage Stock
            </Button>
          </div>
          {/* Added analytics and stock tracking navigation buttons */}
          <div className="grid grid-cols-2 gap-3 mt-3">
            <Button variant="outline" className="h-12 bg-transparent" onClick={() => router.push("/admin/stock")}>
              <BarChart3 className="h-4 w-4 mr-2" />
              Track Stock Levels
            </Button>
            <Button variant="outline" className="h-12 bg-transparent" onClick={() => router.push("/admin/analytics")}>
              <TrendingUp className="h-4 w-4 mr-2" />
              View Analytics
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default DashboardPage
