"use client"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertTriangle, RefreshCw, Wifi, WifiOff, AlertCircle, XCircle } from "lucide-react"

interface ErrorDisplayProps {
  title?: string
  message?: string
  type?: "error" | "warning" | "network" | "not-found"
  onRetry?: () => void
  retryText?: string
  showRetry?: boolean
  className?: string
}

export function ErrorDisplay({
  title,
  message,
  type = "error",
  onRetry,
  retryText = "Try Again",
  showRetry = true,
  className
}: ErrorDisplayProps) {
  const getIcon = () => {
    switch (type) {
      case "network":
        return <WifiOff className="w-8 h-8 text-red-500" />
      case "warning":
        return <AlertTriangle className="w-8 h-8 text-yellow-500" />
      case "not-found":
        return <AlertCircle className="w-8 h-8 text-blue-500" />
      default:
        return <XCircle className="w-8 h-8 text-red-500" />
    }
  }

  const getDefaultTitle = () => {
    switch (type) {
      case "network":
        return "Connection Problem"
      case "warning":
        return "Warning"
      case "not-found":
        return "Not Found"
      default:
        return "Something went wrong"
    }
  }

  const getDefaultMessage = () => {
    switch (type) {
      case "network":
        return "Please check your internet connection and try again."
      case "warning":
        return "Please review the information and try again."
      case "not-found":
        return "The item you're looking for could not be found."
      default:
        return "An unexpected error occurred. Please try again."
    }
  }

  return (
    <div className={cn("flex items-center justify-center p-4", className)}>
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4">
            {getIcon()}
          </div>
          <CardTitle className="text-lg">
            {title || getDefaultTitle()}
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-gray-600">
            {message || getDefaultMessage()}
          </p>
          {showRetry && onRetry && (
            <Button onClick={onRetry} className="w-full">
              <RefreshCw className="w-4 h-4 mr-2" />
              {retryText}
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// Inline error for forms and smaller components
interface InlineErrorProps {
  message: string
  className?: string
}

export function InlineError({ message, className }: InlineErrorProps) {
  return (
    <div className={cn(
      "flex items-center gap-2 text-red-600 text-sm bg-red-50 border border-red-200 rounded p-2",
      className
    )}>
      <AlertTriangle className="w-4 h-4 flex-shrink-0" />
      <span>{message}</span>
    </div>
  )
}

// Network error specifically
export function NetworkError({ onRetry }: { onRetry?: () => void }) {
  return (
    <ErrorDisplay
      type="network"
      title="No Internet Connection"
      message="Please check your connection and try again."
      onRetry={onRetry}
    />
  )
}

// Empty state component
interface EmptyStateProps {
  title?: string
  message?: string
  icon?: React.ReactNode
  action?: React.ReactNode
  className?: string
}

export function EmptyState({
  title = "No items found",
  message = "There are no items to display at the moment.",
  icon,
  action,
  className
}: EmptyStateProps) {
  return (
    <div className={cn("text-center py-12", className)}>
      <div className="mx-auto mb-4 text-gray-400">
        {icon || <AlertCircle className="w-12 h-12" />}
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 mb-4">{message}</p>
      {action}
    </div>
  )
}

// Form field error
interface FieldErrorProps {
  error?: string
  className?: string
}

export function FieldError({ error, className }: FieldErrorProps) {
  if (!error) return null

  return (
    <p className={cn("text-red-600 text-sm mt-1", className)}>
      {error}
    </p>
  )
}

// Toast-like error notification
interface ErrorNotificationProps {
  message: string
  onDismiss?: () => void
  className?: string
}

export function ErrorNotification({ message, onDismiss, className }: ErrorNotificationProps) {
  return (
    <div className={cn(
      "fixed top-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg z-50 max-w-sm",
      className
    )}>
      <div className="flex items-start gap-3">
        <XCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <p className="text-red-800 text-sm">{message}</p>
        </div>
        {onDismiss && (
          <button
            onClick={onDismiss}
            className="text-red-400 hover:text-red-600"
          >
            <XCircle className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  )
}

// Error boundary fallback
export function ErrorFallback({ 
  error, 
  resetError 
}: { 
  error: Error
  resetError: () => void 
}) {
  return (
    <ErrorDisplay
      title="Application Error"
      message={error.message || "An unexpected error occurred"}
      onRetry={resetError}
      retryText="Reload Page"
    />
  )
}
