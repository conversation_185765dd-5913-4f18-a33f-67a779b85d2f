"use client"

import { useEffect, useState } from 'react'
import { useQuery } from 'convex/react'
import { api } from '@/convex/_generated/api'
import { Bell, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { formatDistanceToNow } from 'date-fns'

interface ConvexNotificationsProps {
  userId: string
  isShopOwner?: boolean
}

export function ConvexNotifications({ userId, isShopOwner = false }: ConvexNotificationsProps) {
  const [showNotifications, setShowNotifications] = useState(false)
  
  // Get orders for notifications
  const orders = useQuery(api.orders.list, { userId })
  
  // For shop owners, get all orders
  const allOrders = useQuery(api.orders.list, { userId: isShopOwner ? "all" : userId })

  // Create notifications from orders
  const notifications = orders?.map(order => ({
    id: order._id,
    message: `Order ${order.orderNumber} is ${order.status}`,
    type: order.status,
    read: false,
    createdAt: new Date(order.createdAt).toISOString(),
    orderId: order._id
  })) || []

  const unreadCount = notifications.filter(n => !n.read).length

  // Request notification permission on mount
  useEffect(() => {
    if (Notification.permission === 'default') {
      Notification.requestPermission()
    }
  }, [])

  // Show browser notifications for new orders
  useEffect(() => {
    if (orders && orders.length > 0 && Notification.permission === 'granted') {
      const latestOrder = orders[0]
      if (latestOrder.status === 'pending') {
        new Notification('New Order Received', {
          body: `Order ${latestOrder.orderNumber} - R${latestOrder.total.toFixed(2)}`,
          icon: '/placeholder-logo.png'
        })
      }
    }
  }, [orders])

  const markAsRead = (notificationId: string) => {
    // In a real implementation, you'd update the notification status
    console.log('Marking notification as read:', notificationId)
  }

  const markAllAsRead = () => {
    // In a real implementation, you'd update all notifications
    console.log('Marking all notifications as read')
  }

  return (
    <div className="relative">
      <Button
        variant="outline"
        size="icon"
        onClick={() => setShowNotifications(!showNotifications)}
        className="relative"
      >
        <Bell className="h-4 w-4" />
        {unreadCount > 0 && (
          <Badge 
            variant="destructive" 
            className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs"
          >
            {unreadCount}
          </Badge>
        )}
      </Button>

      {showNotifications && (
        <Card className="absolute right-0 top-12 w-80 z-50 shadow-lg">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm">Order Updates</CardTitle>
              <div className="flex gap-2">
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={markAllAsRead}
                    className="text-xs"
                  >
                    Mark all read
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowNotifications(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <ScrollArea className="h-64">
              {notifications.length === 0 ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  No order updates yet
                </div>
              ) : (
                <div className="space-y-1">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-3 border-b cursor-pointer hover:bg-muted/50 ${
                        !notification.read ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                      }`}
                      onClick={() => markAsRead(notification.id)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className="text-sm font-medium">{notification.message}</p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                          </p>
                        </div>
                        {!notification.read && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1"></div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  )
}


