"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Loader2 } from "lucide-react"

export default function ProductsPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to home page where the product catalog is located
    router.replace("/home")
  }, [router])

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <Card className="w-full max-w-md">
        <CardContent className="p-8 text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <h2 className="text-lg font-semibold mb-2">Loading Products...</h2>
          <p className="text-gray-600">
            Redirecting you to our product catalog
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
