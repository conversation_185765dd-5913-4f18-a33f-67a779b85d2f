"use client"

import type React from "react"

import { useEffect, useState } from "react"
import {
  MapPin,
  CreditCard,
  Bell,
  HelpCircle,
  ChevronRight,
  Edit,
  Star,
  Gift,
  Shield,
  Globe,
  Phone,
  Mail,
  Camera,
  X,
  Save,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import BottomNavigation from "@/components/bottom-navigation"
import { useRouter } from "next/navigation"
import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useConvexCart } from "@/hooks/use-convex-cart"

interface UserProfile {
  name: string
  email: string
  phone: string
  avatar: string
  memberSince: string
  totalOrders: number
  loyaltyPoints: number
  membershipTier: "Bronze" | "Silver" | "Gold" | "Platinum"
}

export default function ProfilePage() {
  const router = useRouter()
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [user, setUser] = useState<any>(null)
  const updateUserMutation = useMutation(api.users.update)

  // Get real-time cart count
  const { cartItems } = useConvexCart("demo-user") // Replace with actual user ID
  const cartCount = cartItems.length

  const userQuery = useQuery(api.users.get, { id: "1" })
  // Get user data from localStorage and Convex on component mount
  useEffect(() => {
    const loadUserData = async () => {
      try {
        // First try to get user from localStorage
        const userData = localStorage.getItem('user')
        if (userData) {
          const parsedUser = JSON.parse(userData)
          setUser(parsedUser)

          // Then verify with Convex
          const userFromDb = await getCurrentUser()
          if (userFromDb) {
            // Update local storage with fresh data
            localStorage.setItem('user', JSON.stringify(userFromDb))
            setUser(userFromDb)
            updateUserProfileState(userFromDb)
          } else {
            updateUserProfileState(parsedUser)
          }
        } else {
          // If no user in localStorage, redirect to login
          router.push('/login')
        }
      } catch (error) {
        console.error('Error loading user data:', error)
        // Fallback to empty state
        setUserProfile({
          name: '',
          email: '',
          phone: '',
          avatar: "/placeholder.svg?height=80&width=80&text=U",
          memberSince: new Date().toISOString().split('T')[0],
          totalOrders: 0,
          loyaltyPoints: 0,
          membershipTier: 'Bronze'
        })
      }
    }

    loadUserData()
  }, [router])

  // Helper function to update the user profile state
  const updateUserProfileState = (userData: any) => {
    setUserProfile({
      name: userData.name || '',
      email: userData.email || '',
      phone: userData.phone || '',
      avatar: userData.avatar || "/placeholder.svg?height=80&width=80&text=U",
      memberSince: userData._creationTime 
        ? new Date(userData._creationTime).toISOString().split('T')[0] 
        : new Date().toISOString().split('T')[0],
      totalOrders: userData.totalOrders || 0,
      loyaltyPoints: userData.loyaltyPoints || 0,
      membershipTier: userData.membershipTier || 'Bronze'
    })

    // Update the edit form with current user data
    setEditForm({
      name: userData.name || '',
      phone: userData.phone || '',
      avatar: userData.avatar || ''
    })
  }

  // Use Convex query to get user data
  const userData = useQuery(api.users.get, { id: user?.email || '' });

  // Update local state when user data changes
  useEffect(() => {
    if (userData) {
      setUser(userData);
      updateUserProfileState(userData);
    }
  }, [userData]);

  // Function to get current user from localStorage
  const getCurrentUser = () => {
    try {
      const userData = localStorage.getItem('user');
      if (!userData) return null;
      
      const parsedUser = JSON.parse(userData);
      return parsedUser;
    } catch (error) {
      console.error('Error getting user from localStorage:', error);
      return null;
    }
  }

  const [userProfile, setUserProfile] = useState<UserProfile>({
    name: "",
    email: "",
    phone: "",
    avatar: "/placeholder.svg?height=80&width=80&text=U",
    memberSince: new Date().toISOString().split('T')[0],
    totalOrders: 0,
    loyaltyPoints: 0,
    membershipTier: "Bronze",
  })

  const [editForm, setEditForm] = useState({
    name: "",
    phone: "",
    avatar: "",
  })

  const handleEditProfile = () => {
    setEditForm({
      name: userProfile.name,
      phone: userProfile.phone,
      avatar: userProfile.avatar,
    })
    setIsEditModalOpen(true)
  }

  const handleSaveProfile = async () => {
    if (!user || !user._id) {
      console.error('Cannot update profile: User ID is not available');
      // Try to reload user data
      const refreshedUser = await getCurrentUser();
      if (!refreshedUser || !refreshedUser._id) {
        alert('Your session has expired. Please log in again.');
        router.push('/login');
        return;
      }
      setUser(refreshedUser);
    }
    
    setIsLoading(true);
    try {
      // Update user in the database
      const updatedUser = await updateUserMutation({
        id: user._id,
        name: editForm.name,
        phone: editForm.phone,
        avatar: editForm.avatar,
      });

      if (!updatedUser) {
        throw new Error('Failed to update profile');
      }

      // Update local state
      const updatedProfile = {
        ...userProfile,
        name: editForm.name,
        phone: editForm.phone,
        avatar: editForm.avatar,
      };
      
      setUserProfile(updatedProfile);
      
      // Safely merge user data with proper null checks
      const updatedUserData = {
        ...(user || {}), // Safely spread user if it exists
        ...(updatedUser || {}),  // Safely spread updatedUser if it exists
        ...updatedProfile // Spread the updated profile data
      };
      
      // Ensure we have valid user data before saving
      if (!updatedUserData.email) {
        throw new Error('Invalid user data: Missing email');
      }
      
      localStorage.setItem('user', JSON.stringify(updatedUserData));
      setUser(updatedUserData);
      
      setIsEditModalOpen(false);
    } catch (error) {
      console.error("Failed to update profile:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setEditForm((prev) => ({
          ...prev,
          avatar: e.target?.result as string,
        }))
      }
      reader.readAsDataURL(file)
    }
  }

  const menuSections = [
    {
      title: "Account",
      items: [
        {
          icon: <Edit className="w-5 h-5" />,
          label: "Edit Profile",
          description: "Update your personal information",
          action: handleEditProfile, // Connected to real edit functionality
        },
        {
          icon: <MapPin className="w-5 h-5" />,
          label: "Delivery Addresses",
          description: "Manage your delivery locations",
          badge: "3",
          action: () => console.log("Manage addresses"),
        },
        {
          icon: <CreditCard className="w-5 h-5" />,
          label: "Payment Methods",
          description: "Cards and payment options",
          badge: "2",
          action: () => router.push("/payments/pages"),
        },
      ],
    },
    {
      title: "Preferences",
      items: [
        {
          icon: <Bell className="w-5 h-5" />,
          label: "Notifications",
          description: "Push notifications and alerts",
          action: () => console.log("Notification settings"),
        },
        {
          icon: <Globe className="w-5 h-5" />,
          label: "Language & Region",
          description: "English (South Africa)",
          action: () => router.push("/language"), // Navigate to language settings page
        },
        {
          icon: <Shield className="w-5 h-5" />,
          label: "Privacy & Security",
          description: "Account security settings",
          action: () => console.log("Privacy settings"),
        },
      ],
    },
    {
      title: "Support",
      items: [
        {
          icon: <HelpCircle className="w-5 h-5" />,
          label: "Help Center",
          description: "FAQs and support articles",
          action: () => router.push("/support"), // Navigate to support page
        },
        {
          icon: <Phone className="w-5 h-5" />,
          label: "Contact Support",
          description: "Get help from our team",
          action: () => router.push("/support"), // Navigate to support page
        },
        {
          icon: <Star className="w-5 h-5" />,
          label: "Rate Our App",
          description: "Share your feedback",
          action: () => console.log("Rate app"),
        },
      ],
    },
  ]

  const getTierColor = (tier: UserProfile["membershipTier"]) => {
    switch (tier) {
      case "Bronze":
        return "bg-amber-100 text-amber-800"
      case "Silver":
        return "bg-gray-100 text-gray-800"
      case "Gold":
        return "bg-yellow-100 text-yellow-800"
      case "Platinum":
        return "bg-purple-100 text-purple-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const handleLogout = () => {
    router.push("/login")
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-lg font-semibold">My Profile</h1>
            <p className="text-blue-100 text-sm">Manage your account</p>
          </div>
          <div className="relative">
            <Button
              size="sm"
              className="absolute -bottom-1 -right-1 w-8 h-8 p-0 bg-blue-600 hover:bg-blue-700 rounded-full"
              onClick={handleEditProfile} // Added click handler for camera button
            >
              <Camera className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Profile Card */}
      <div className="bg-white m-4 rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center gap-4 mb-4">
          <div className="relative">
            <Avatar className="w-20 h-20">
              <AvatarImage src={userProfile.avatar || "/placeholder.svg"} alt={userProfile.name} />
              <AvatarFallback className="bg-blue-100 text-blue-600 text-xl font-semibold">
                {userProfile.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
          </div>
          <div className="flex-1">
            <h2 className="text-xl font-semibold text-gray-900">{userProfile.name}</h2>
            <div className="flex items-center gap-2 mb-1">
              <Mail className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">{userProfile.email}</span>
            </div>
            <div className="flex items-center gap-2">
              <Phone className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">{userProfile.phone}</span>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <Badge className={`${getTierColor(userProfile.membershipTier)} border-0`}>
            <Star className="w-3 h-3 mr-1" />
            {userProfile.membershipTier} Member
          </Badge>
          <span className="text-sm text-gray-500">
            Member since {new Date(userProfile.memberSince).toLocaleDateString()}
          </span>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="mx-4 mb-6 grid grid-cols-3 gap-3">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{userProfile.totalOrders}</div>
          <div className="text-xs text-gray-600">Total Orders</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
          <div className="text-2xl font-bold text-green-600">{userProfile.loyaltyPoints}</div>
          <div className="text-xs text-gray-600">Loyalty Points</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
          <div className="text-2xl font-bold text-purple-600">R 50</div>
          <div className="text-xs text-gray-600">Saved This Month</div>
        </div>
      </div>

      {/* Loyalty Points Banner */}
      <div className="mx-4 mb-6 bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold mb-1">Loyalty Rewards</h3>
            <p className="text-purple-100 text-sm">Earn points with every order</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">{userProfile.loyaltyPoints}</div>
            <div className="text-purple-100 text-xs">Points Available</div>
          </div>
        </div>
        <Button
          variant="secondary"
          size="sm"
          className="mt-3 bg-white text-purple-600 hover:bg-gray-100"
          onClick={() => router.push("/loyalty")} // Navigate to loyalty rewards page
        >
          <Gift className="w-4 h-4 mr-1" />
          View Rewards
        </Button>
      </div>

      {/* Menu Sections */}
      <div className="px-4 space-y-6">
        {menuSections.map((section) => (
          <div key={section.title}>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">{section.title}</h3>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              {section.items.map((item, index) => (
                <button
                  key={item.label}
                  className={`w-full flex items-center gap-4 p-4 text-left hover:bg-gray-50 transition-colors ${
                    index !== section.items.length - 1 ? "border-b border-gray-100" : ""
                  }`}
                  onClick={item.action}
                >
                  <div className="text-gray-600">{item.icon}</div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-900">{item.label}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="bg-blue-100 text-blue-600 text-xs">
                          {item.badge}
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-500">{item.description}</p>
                  </div>
                  <ChevronRight className="w-5 h-5 text-gray-400" />
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Edit Profile Modal */}
      {isEditModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg w-full max-w-md">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">Edit Profile</h2>
              <Button variant="ghost" size="sm" onClick={() => setIsEditModalOpen(false)}>
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="p-4 space-y-4">
              {/* Avatar Upload */}
              <div className="flex flex-col items-center gap-3">
                <Avatar className="w-20 h-20">
                  <AvatarImage src={editForm.avatar || "/placeholder.svg"} alt="Profile" />
                  <AvatarFallback className="bg-blue-100 text-blue-600 text-xl font-semibold">
                    {editForm.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarChange}
                    className="hidden"
                    id="avatar-upload"
                  />
                  <Label
                    htmlFor="avatar-upload"
                    className="cursor-pointer bg-blue-600 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-700"
                  >
                    Change Photo
                  </Label>
                </div>
              </div>

              {/* Name Field */}
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  value={editForm.name}
                  onChange={(e) => setEditForm((prev) => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter your full name"
                />
              </div>

              {/* Phone Field */}
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  value={editForm.phone}
                  onChange={(e) => setEditForm((prev) => ({ ...prev, phone: e.target.value }))}
                  placeholder="Enter your phone number"
                />
              </div>

              {/* Email Field (Read-only) */}
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input id="email" value={userProfile.email} disabled className="bg-gray-50" />
                <p className="text-xs text-gray-500">Email cannot be changed</p>
              </div>
            </div>

            <div className="flex gap-3 p-4 border-t">
              <Button
                variant="outline"
                className="flex-1 bg-transparent"
                onClick={() => setIsEditModalOpen(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button className="flex-1 bg-blue-600 hover:bg-blue-700" onClick={handleSaveProfile} disabled={isLoading}>
                {isLoading ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                Save Changes
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Shared Bottom Navigation */}
      <BottomNavigation cartCount={cartCount} />

      {/* Bottom padding to account for fixed navigation */}
      <div className="h-20"></div>
    </div>
  )
}
