import { query, mutation } from "./_generated/server"
import { v } from "convex/values"

export const list = query({
  args: { category: v.optional(v.string()) },
  handler: async (ctx, args) => {
    if (args.category && args.category !== "all") {
      return await ctx.db
        .query("products")
        .withIndex("by_category", (q) => q.eq("category", args.category as string))
        .collect()
    }
    return await ctx.db.query("products").collect()
  },
})

export const get = query({
  args: { id: v.id("products") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id)
  },
})

export const search = query({
  args: { query: v.string() },
  handler: async (ctx, args) => {
    const products = await ctx.db.query("products").collect()
    return products.filter((product) => product.name.toLowerCase().includes(args.query.toLowerCase()))
  },
})

export const create = mutation({
  args: {
    name: v.string(),
    price: v.number(),
    originalPrice: v.optional(v.number()),
    image: v.string(),
    category: v.string(),
    rating: v.optional(v.number()),
    reviews: v.optional(v.number()),
    inStock: v.optional(v.boolean()),
    discount: v.optional(v.number()),
    isSpecialOffer: v.optional(v.boolean()),
    stockQuantity: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("products", {
      ...args,
      inStock: args.inStock ?? true,
      stockQuantity: args.stockQuantity ?? 0,
    })
  },
})

export const update = mutation({
  args: {
    id: v.id("products"),
    name: v.optional(v.string()),
    price: v.optional(v.number()),
    originalPrice: v.optional(v.number()),
    image: v.optional(v.string()),
    category: v.optional(v.string()),
    rating: v.optional(v.number()),
    reviews: v.optional(v.number()),
    inStock: v.optional(v.boolean()),
    discount: v.optional(v.number()),
    isSpecialOffer: v.optional(v.boolean()),
    stockQuantity: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args
    await ctx.db.patch(id, updates)
  },
})

export const updateStock = mutation({
  args: { 
    id: v.id("products"),
    stockQuantity: v.number(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.id, { 
      stockQuantity: args.stockQuantity,
      inStock: args.stockQuantity > 0
    })
  },
})

export const deleteProduct = mutation({
  args: { id: v.id("products") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.id)
  },
})

export const purchaseProduct = mutation({
  args: {
    productId: v.id("products"),
    quantity: v.number(),
  },
  handler: async (ctx, args) => {
    const product = await ctx.db.get(args.productId)
    if (!product) {
      throw new Error("Product not found")
    }
    
    if (!product.inStock || (product.stockQuantity && product.stockQuantity < args.quantity)) {
      throw new Error("Insufficient stock")
    }
    
    const newStockQuantity = (product.stockQuantity || 0) - args.quantity
    await ctx.db.patch(args.productId, {
      stockQuantity: newStockQuantity,
      inStock: newStockQuantity > 0
    })
    
    return { success: true, remainingStock: newStockQuantity }
  },
})