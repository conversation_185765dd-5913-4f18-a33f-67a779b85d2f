"use client"

import { useEffect, useCallback } from "react"
import { captureMetric, captureError } from "@/lib/error-tracking"
import { measureAsyncPerformance, debounce } from "@/lib/cache"

export function usePerformanceMonitoring() {
  // Monitor component render times
  const measureRenderTime = useCallback((componentName: string, startTime: number) => {
    const endTime = performance.now()
    const renderTime = endTime - startTime

    captureMetric({
      metric: "render_time",
      value: renderTime,
      metadata: {
        component: componentName,
      },
    })

    // Log slow renders
    if (renderTime > 100) {
      captureError({
        message: `Slow render detected in ${componentName}`,
        severity: "medium",
        category: "ui",
        metadata: {
          component: componentName,
          renderTime,
        },
      })
    }
  }, [])

  // Monitor API call performance
  const measureApiCall = useCallback(async <T>(
    apiCall: () => Promise<T>,
    endpoint: string
  ): Promise<T> => {
    const startTime = performance.now()
    
    try {
      const result = await apiCall()
      const endTime = performance.now()
      const duration = endTime - startTime

      captureMetric({
        metric: "api_call",
        value: duration,
        metadata: {
          endpoint,
          success: true,
        },
      })

      // Log slow API calls
      if (duration > 2000) {
        captureError({
          message: `Slow API call to ${endpoint}`,
          severity: "medium",
          category: "network",
          metadata: {
            endpoint,
            duration,
          },
        })
      }

      return result
    } catch (error) {
      const endTime = performance.now()
      const duration = endTime - startTime

      captureError({
        message: `API call failed: ${endpoint}`,
        stack: error instanceof Error ? error.stack : undefined,
        severity: "high",
        category: "network",
        metadata: {
          endpoint,
          duration,
          error: error instanceof Error ? error.message : String(error),
        },
      })

      throw error
    }
  }, [])

  // Monitor user interactions
  const measureInteraction = useCallback((interactionType: string, element?: string) => {
    const startTime = performance.now()

    return () => {
      const endTime = performance.now()
      const duration = endTime - startTime

      captureMetric({
        metric: "interaction",
        value: duration,
        metadata: {
          type: interactionType,
          element,
        },
      })
    }
  }, [])

  // Monitor memory usage
  const checkMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      
      captureMetric({
        metric: "page_load",
        value: memory.usedJSHeapSize,
        metadata: {
          type: "memory_usage",
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
        },
      })

      // Alert on high memory usage
      const memoryUsagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
      if (memoryUsagePercent > 80) {
        captureError({
          message: `High memory usage detected: ${memoryUsagePercent.toFixed(1)}%`,
          severity: "medium",
          category: "ui",
          metadata: {
            memoryUsagePercent,
            usedJSHeapSize: memory.usedJSHeapSize,
            jsHeapSizeLimit: memory.jsHeapSizeLimit,
          },
        })
      }
    }
  }, [])

  // Monitor Core Web Vitals
  useEffect(() => {
    if (typeof window === "undefined") return

    // Largest Contentful Paint (LCP)
    const observeLCP = () => {
      if ('PerformanceObserver' in window) {
        try {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            const lastEntry = entries[entries.length - 1]
            
            captureMetric({
              metric: "page_load",
              value: lastEntry.startTime,
              metadata: {
                type: "lcp",
                element: (lastEntry as any).element?.tagName,
              },
            })

            // Alert on poor LCP
            if (lastEntry.startTime > 2500) {
              captureError({
                message: `Poor LCP detected: ${lastEntry.startTime.toFixed(0)}ms`,
                severity: "medium",
                category: "ui",
                metadata: {
                  lcp: lastEntry.startTime,
                  element: (lastEntry as any).element?.tagName,
                },
              })
            }
          })
          
          observer.observe({ entryTypes: ['largest-contentful-paint'] })
        } catch (e) {
          // Observer not supported
        }
      }
    }

    // First Input Delay (FID)
    const observeFID = () => {
      if ('PerformanceObserver' in window) {
        try {
          const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              captureMetric({
                metric: "interaction",
                value: (entry as any).processingStart - entry.startTime,
                metadata: {
                  type: "fid",
                  eventType: entry.name,
                },
              })

              // Alert on poor FID
              const fid = (entry as any).processingStart - entry.startTime
              if (fid > 100) {
                captureError({
                  message: `Poor FID detected: ${fid.toFixed(0)}ms`,
                  severity: "medium",
                  category: "ui",
                  metadata: {
                    fid,
                    eventType: entry.name,
                  },
                })
              }
            }
          })
          
          observer.observe({ entryTypes: ['first-input'] })
        } catch (e) {
          // Observer not supported
        }
      }
    }

    // Cumulative Layout Shift (CLS)
    const observeCLS = () => {
      if ('PerformanceObserver' in window) {
        try {
          let clsValue = 0
          
          const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (!(entry as any).hadRecentInput) {
                clsValue += (entry as any).value
              }
            }

            captureMetric({
              metric: "page_load",
              value: clsValue,
              metadata: {
                type: "cls",
              },
            })

            // Alert on poor CLS
            if (clsValue > 0.1) {
              captureError({
                message: `Poor CLS detected: ${clsValue.toFixed(3)}`,
                severity: "medium",
                category: "ui",
                metadata: {
                  cls: clsValue,
                },
              })
            }
          })
          
          observer.observe({ entryTypes: ['layout-shift'] })
        } catch (e) {
          // Observer not supported
        }
      }
    }

    observeLCP()
    observeFID()
    observeCLS()

    // Check memory usage periodically
    const memoryInterval = setInterval(checkMemoryUsage, 30000) // Every 30 seconds

    return () => {
      clearInterval(memoryInterval)
    }
  }, [checkMemoryUsage])

  return {
    measureRenderTime,
    measureApiCall,
    measureInteraction,
    checkMemoryUsage,
  }
}
