"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, Gift, Star, Users, Trophy, Zap, Crown, Share2, Co<PERSON>, CheckCircle, Award } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function LoyaltyRewardsPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("overview")
  const [referralCopied, setReferralCopied] = useState(false)

  const userLoyalty = {
    currentPoints: 1250,
    currentTier: "Gold",
    nextTier: "Platinum",
    pointsToNextTier: 750,
    totalPointsEarned: 3450,
    referralCode: "JOHN2024",
    referralsCount: 8,
    monthlySpend: 1850,
  }

  const tiers = [
    {
      name: "Bronze",
      minPoints: 0,
      color: "bg-amber-100 text-amber-800",
      benefits: ["1 point per R10 spent", "Birthday discount", "Free delivery on orders over R200"],
    },
    {
      name: "Silver",
      minPoints: 500,
      color: "bg-gray-100 text-gray-800",
      benefits: ["1.5 points per R10 spent", "Priority support", "Free delivery on orders over R150"],
    },
    {
      name: "Gold",
      minPoints: 1000,
      color: "bg-yellow-100 text-yellow-800",
      benefits: ["2 points per R10 spent", "Early access to sales", "Free delivery on orders over R100"],
    },
    {
      name: "Platinum",
      minPoints: 2000,
      color: "bg-purple-100 text-purple-800",
      benefits: ["3 points per R10 spent", "Personal shopper", "Always free delivery", "Exclusive products"],
    },
  ]

  const rewards = [
    {
      id: "reward-1",
      title: "R50 Voucher",
      points: 500,
      description: "R50 off your next order",
      category: "voucher",
      available: true,
    },
    {
      id: "reward-2",
      title: "Free Delivery (Month)",
      points: 300,
      description: "Free delivery for 30 days",
      category: "delivery",
      available: true,
    },
    {
      id: "reward-3",
      title: "R100 Voucher",
      points: 1000,
      description: "R100 off your next order",
      category: "voucher",
      available: true,
    },
    {
      id: "reward-4",
      title: "Premium Support",
      points: 750,
      description: "Priority customer support for 3 months",
      category: "service",
      available: true,
    },
    {
      id: "reward-5",
      title: "R200 Voucher",
      points: 2000,
      description: "R200 off your next order",
      category: "voucher",
      available: false,
    },
  ]

  const achievements = [
    {
      id: "ach-1",
      title: "First Order",
      description: "Complete your first order",
      points: 50,
      completed: true,
      icon: <Gift className="w-6 h-6" />,
    },
    {
      id: "ach-2",
      title: "Loyal Customer",
      description: "Complete 10 orders",
      points: 200,
      completed: true,
      icon: <Star className="w-6 h-6" />,
    },
    {
      id: "ach-3",
      title: "Community Builder",
      description: "Refer 5 friends",
      points: 300,
      completed: true,
      icon: <Users className="w-6 h-6" />,
    },
    {
      id: "ach-4",
      title: "Big Spender",
      description: "Spend R5000 in total",
      points: 500,
      completed: false,
      progress: 75,
      icon: <Trophy className="w-6 h-6" />,
    },
    {
      id: "ach-5",
      title: "Speed Shopper",
      description: "Complete 5 orders in one week",
      points: 150,
      completed: false,
      progress: 40,
      icon: <Zap className="w-6 h-6" />,
    },
  ]

  const recentActivity = [
    {
      id: "act-1",
      type: "earned",
      description: "Order #ORD-2024-001",
      points: 45,
      date: "2024-01-10",
    },
    {
      id: "act-2",
      type: "redeemed",
      description: "R50 Voucher",
      points: -500,
      date: "2024-01-08",
    },
    {
      id: "act-3",
      type: "bonus",
      description: "Referral bonus - Sarah M.",
      points: 100,
      date: "2024-01-05",
    },
    {
      id: "act-4",
      type: "earned",
      description: "Order #ORD-2024-002",
      points: 32,
      date: "2024-01-03",
    },
  ]

  const copyReferralCode = () => {
    navigator.clipboard.writeText(userLoyalty.referralCode)
    setReferralCopied(true)
    setTimeout(() => setReferralCopied(false), 2000)
  }

  const redeemReward = (rewardId: string, points: number) => {
    if (userLoyalty.currentPoints >= points) {
      alert(`Reward redeemed! ${points} points deducted.`)
    } else {
      alert("Insufficient points!")
    }
  }

  const currentTierIndex = tiers.findIndex((tier) => tier.name === userLoyalty.currentTier)
  const progressPercentage =
    currentTierIndex < tiers.length - 1
      ? ((userLoyalty.currentPoints - tiers[currentTierIndex].minPoints) /
          (tiers[currentTierIndex + 1].minPoints - tiers[currentTierIndex].minPoints)) *
        100
      : 100

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-4">
        <div className="flex items-center gap-3 mb-4">
          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-purple-800 p-2"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-lg font-semibold">Loyalty Rewards</h1>
        </div>

        {/* Points Summary */}
        <div className="bg-white bg-opacity-20 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <div>
              <h2 className="text-2xl font-bold">{userLoyalty.currentPoints}</h2>
              <p className="text-purple-100">Available Points</p>
            </div>
            <Badge className="bg-yellow-500 text-yellow-900 border-0">
              <Crown className="w-3 h-3 mr-1" />
              {userLoyalty.currentTier}
            </Badge>
          </div>

          {currentTierIndex < tiers.length - 1 && (
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Progress to {userLoyalty.nextTier}</span>
                <span>{userLoyalty.pointsToNextTier} points to go</span>
              </div>
              <Progress value={progressPercentage} className="h-2 bg-purple-800" />
            </div>
          )}
        </div>
      </div>

      <div className="p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="rewards">Rewards</TabsTrigger>
            <TabsTrigger value="referrals">Referrals</TabsTrigger>
            <TabsTrigger value="achievements">Badges</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Membership Tiers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="w-5 h-5 text-purple-600" />
                  Membership Tiers
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {tiers.map((tier, index) => (
                  <div
                    key={tier.name}
                    className={`p-3 rounded-lg border ${
                      tier.name === userLoyalty.currentTier
                        ? "border-purple-300 bg-purple-50"
                        : "border-gray-200 bg-gray-50"
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge className={tier.color}>{tier.name}</Badge>
                        {tier.name === userLoyalty.currentTier && (
                          <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                            Current
                          </Badge>
                        )}
                      </div>
                      <span className="text-sm text-gray-600">{tier.minPoints}+ points</span>
                    </div>
                    <div className="space-y-1">
                      {tier.benefits.map((benefit, idx) => (
                        <p key={idx} className="text-xs text-gray-600">
                          • {benefit}
                        </p>
                      ))}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {recentActivity.map((activity) => (
                  <div
                    key={activity.id}
                    className="flex items-center justify-between p-2 border-b border-gray-100 last:border-b-0"
                  >
                    <div>
                      <p className="font-medium text-sm">{activity.description}</p>
                      <p className="text-xs text-gray-500">{activity.date}</p>
                    </div>
                    <div className={`font-semibold ${activity.points > 0 ? "text-green-600" : "text-red-600"}`}>
                      {activity.points > 0 ? "+" : ""}
                      {activity.points} pts
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="rewards" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gift className="w-5 h-5 text-purple-600" />
                  Available Rewards
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {rewards.map((reward) => (
                  <div
                    key={reward.id}
                    className={`p-4 border rounded-lg ${
                      reward.available && userLoyalty.currentPoints >= reward.points
                        ? "border-green-200 bg-green-50"
                        : "border-gray-200 bg-gray-50"
                    }`}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h3 className="font-semibold text-gray-900">{reward.title}</h3>
                        <p className="text-sm text-gray-600">{reward.description}</p>
                      </div>
                      <Badge
                        variant="secondary"
                        className={
                          reward.available && userLoyalty.currentPoints >= reward.points
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-600"
                        }
                      >
                        {reward.points} pts
                      </Badge>
                    </div>
                    <Button
                      size="sm"
                      disabled={!reward.available || userLoyalty.currentPoints < reward.points}
                      onClick={() => redeemReward(reward.id, reward.points)}
                      className={
                        reward.available && userLoyalty.currentPoints >= reward.points
                          ? "bg-purple-600 hover:bg-purple-700"
                          : "bg-gray-300"
                      }
                    >
                      {userLoyalty.currentPoints >= reward.points ? "Redeem" : "Insufficient Points"}
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="referrals" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Share2 className="w-5 h-5 text-purple-600" />
                  Refer Friends & Earn
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <h3 className="font-semibold text-purple-900 mb-2">Earn 100 Points Per Referral!</h3>
                  <p className="text-sm text-purple-700 mb-4">
                    Share your referral code and earn points when friends make their first order
                  </p>
                  <div className="flex items-center gap-2 justify-center">
                    <code className="bg-white px-3 py-2 rounded border text-lg font-mono">
                      {userLoyalty.referralCode}
                    </code>
                    <Button size="sm" onClick={copyReferralCode} className="bg-purple-600 hover:bg-purple-700">
                      {referralCopied ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                    </Button>
                  </div>
                  {referralCopied && <p className="text-sm text-green-600 mt-2">Copied to clipboard!</p>}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{userLoyalty.referralsCount}</div>
                    <div className="text-sm text-blue-700">Friends Referred</div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{userLoyalty.referralsCount * 100}</div>
                    <div className="text-sm text-green-700">Points Earned</div>
                  </div>
                </div>

                <Button className="w-full bg-purple-600 hover:bg-purple-700">
                  <Share2 className="w-4 h-4 mr-2" />
                  Share Referral Code
                </Button>
              </CardContent>
            </Card>

            {/* Community Rewards */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-purple-600" />
                  Community Rewards
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="p-3 border rounded-lg">
                  <h4 className="font-semibold text-gray-900">Group Orders</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    Organize group orders with neighbors and earn bonus points
                  </p>
                  <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                    +50 points per group order
                  </Badge>
                </div>
                <div className="p-3 border rounded-lg">
                  <h4 className="font-semibold text-gray-900">Community Champion</h4>
                  <p className="text-sm text-gray-600 mb-2">Help new users in your area get started</p>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    +25 points per new user helped
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="achievements" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5 text-purple-600" />
                  Achievement Badges
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {achievements.map((achievement) => (
                  <div
                    key={achievement.id}
                    className={`p-4 border rounded-lg ${
                      achievement.completed ? "border-green-200 bg-green-50" : "border-gray-200 bg-gray-50"
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <div
                        className={`p-2 rounded-full ${
                          achievement.completed ? "bg-green-100 text-green-600" : "bg-gray-100 text-gray-400"
                        }`}
                      >
                        {achievement.icon}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <h3 className="font-semibold text-gray-900">{achievement.title}</h3>
                          <Badge
                            variant="secondary"
                            className={
                              achievement.completed ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-600"
                            }
                          >
                            {achievement.points} pts
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{achievement.description}</p>
                        {!achievement.completed && achievement.progress && (
                          <div>
                            <div className="flex justify-between text-xs text-gray-500 mb-1">
                              <span>Progress</span>
                              <span>{achievement.progress}%</span>
                            </div>
                            <Progress value={achievement.progress} className="h-2" />
                          </div>
                        )}
                        {achievement.completed && (
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Completed
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
