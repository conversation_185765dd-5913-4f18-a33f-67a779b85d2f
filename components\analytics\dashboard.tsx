"use client"

import { useState, useEffect } from "react"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  AlertTriangle,
  Zap,
  Clock
} from "lucide-react"
import { formatCurrency } from "@/lib/payment"
import { LoadingSpinner } from "@/components/ui/loading"
import { ErrorDisplay } from "@/components/ui/error-display"
import { useErrorTracking } from "@/lib/error-tracking"

interface AnalyticsDashboardProps {
  shopOwnerId?: string
}

export function AnalyticsDashboard({ shopOwnerId }: AnalyticsDashboardProps) {
  const [timeRange, setTimeRange] = useState("7d")
  const [selectedMetric, setSelectedMetric] = useState("revenue")
  const { getStoredErrors, getStoredMetrics } = useErrorTracking()

  // Calculate date range
  const getDateRange = () => {
    const now = Date.now()
    const ranges = {
      "1d": now - 24 * 60 * 60 * 1000,
      "7d": now - 7 * 24 * 60 * 60 * 1000,
      "30d": now - 30 * 24 * 60 * 60 * 1000,
      "90d": now - 90 * 24 * 60 * 60 * 1000,
    }
    return ranges[timeRange as keyof typeof ranges] || ranges["7d"]
  }

  // Get monitoring data
  const [errors, setErrors] = useState<any[]>([])
  const [metrics, setMetrics] = useState<any[]>([])

  useEffect(() => {
    setErrors(getStoredErrors())
    setMetrics(getStoredMetrics())
  }, [])

  // Fetch analytics data
  const analytics = useQuery(api.payments.getPaymentAnalytics, {
    startDate: getDateRange(),
    endDate: Date.now(),
  })

  const orders = useQuery(api.orders.list, { userId: "shop-owner" })
  const products = useQuery(api.products.list, {})

  const loading = analytics === undefined || orders === undefined || products === undefined

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!analytics || !orders || !products) {
    return (
      <ErrorDisplay
        title="Failed to load analytics"
        message="Unable to fetch analytics data. Please try again."
        onRetry={() => window.location.reload()}
      />
    )
  }

  // Calculate metrics
  const totalRevenue = analytics.totalRevenue
  const totalOrders = analytics.totalOrders
  const averageOrderValue = analytics.averageOrderValue
  const totalProducts = products.length

  // Calculate growth (mock data for demo)
  const revenueGrowth = 12.5
  const orderGrowth = 8.3
  const customerGrowth = 15.2

  // Top products (mock calculation)
  const topProducts = products.slice(0, 5).map(product => ({
    ...product,
    sales: Math.floor(Math.random() * 100) + 10,
    revenue: Math.floor(Math.random() * 5000) + 500,
  }))

  const MetricCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    trend 
  }: {
    title: string
    value: string | number
    change?: number
    icon: any
    trend?: "up" | "down"
  }) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {change !== undefined && (
              <div className={`flex items-center text-sm ${
                trend === "up" ? "text-green-600" : "text-red-600"
              }`}>
                {trend === "up" ? (
                  <TrendingUp className="w-4 h-4 mr-1" />
                ) : (
                  <TrendingDown className="w-4 h-4 mr-1" />
                )}
                {Math.abs(change)}% from last period
              </div>
            )}
          </div>
          <div className="p-3 bg-blue-50 rounded-full">
            <Icon className="w-6 h-6 text-blue-600" />
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">Analytics & Monitoring</h1>
          <p className="text-gray-600">Track your business performance and system health</p>
        </div>
        <div className="flex gap-3">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1d">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Calendar className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <Tabs defaultValue="analytics" className="space-y-6">
        <TabsList>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
        </TabsList>

        <TabsContent value="analytics" className="space-y-6">

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Revenue"
          value={formatCurrency(totalRevenue)}
          change={revenueGrowth}
          icon={DollarSign}
          trend="up"
        />
        <MetricCard
          title="Total Orders"
          value={totalOrders}
          change={orderGrowth}
          icon={ShoppingCart}
          trend="up"
        />
        <MetricCard
          title="Average Order Value"
          value={formatCurrency(averageOrderValue)}
          change={-2.1}
          icon={TrendingUp}
          trend="down"
        />
        <MetricCard
          title="Active Products"
          value={totalProducts}
          icon={Package}
        />
      </div>

      {/* Charts and Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Payment Methods */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="w-5 h-5" />
              Payment Methods
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(analytics.paymentMethods).map(([method, count]) => {
                const percentage = (count / totalOrders) * 100
                return (
                  <div key={method} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="capitalize">{method.replace('_', ' ')}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">{count} orders</span>
                      <Badge variant="secondary">{percentage.toFixed(1)}%</Badge>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Order Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Order Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(analytics.paymentStatuses).map(([status, count]) => {
                const percentage = (count / totalOrders) * 100
                const statusColors = {
                  pending: "bg-yellow-500",
                  processing: "bg-blue-500",
                  completed: "bg-green-500",
                  failed: "bg-red-500",
                  refunded: "bg-gray-500",
                }
                return (
                  <div key={status} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${statusColors[status as keyof typeof statusColors]}`}></div>
                      <span className="capitalize">{status}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">{count} orders</span>
                      <Badge variant="secondary">{percentage.toFixed(1)}%</Badge>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Products */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Top Performing Products
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topProducts.map((product, index) => (
              <div key={product._id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center text-sm font-medium">
                    #{index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{product.name}</p>
                    <p className="text-sm text-gray-600">{product.category}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">{formatCurrency(product.revenue)}</p>
                  <p className="text-sm text-gray-600">{product.sales} sold</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-6">
          {/* Error Monitoring */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <MetricCard
              title="Total Errors"
              value={errors.length}
              icon={AlertTriangle}
            />
            <MetricCard
              title="Critical Errors"
              value={errors.filter(e => e.severity === "critical").length}
              icon={AlertTriangle}
            />
            <MetricCard
              title="Avg Response Time"
              value={`${Math.round(metrics.filter(m => m.metric === "api_call").reduce((acc, m) => acc + m.value, 0) / Math.max(metrics.filter(m => m.metric === "api_call").length, 1))}ms`}
              icon={Zap}
            />
          </div>

          {/* Recent Errors */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Recent Errors
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {errors.slice(-5).reverse().map((error) => (
                  <div key={error.id} className="p-3 border rounded-lg">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant={error.severity === "critical" ? "destructive" : "secondary"}>
                            {error.severity}
                          </Badge>
                          <span className="text-sm text-gray-500">{error.category}</span>
                        </div>
                        <p className="font-medium text-sm">{error.message}</p>
                        <p className="text-xs text-gray-500 mt-1">{error.url}</p>
                      </div>
                      <span className="text-xs text-gray-500">
                        {new Date(error.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                ))}
                {errors.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No errors recorded
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Performance Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Performance Metrics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {metrics.slice(-5).reverse().map((metric) => (
                  <div key={metric.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium text-sm capitalize">{metric.metric.replace('_', ' ')}</p>
                      <p className="text-xs text-gray-500">{metric.url}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{Math.round(metric.value)}ms</p>
                      <p className="text-xs text-gray-500">
                        {new Date(metric.timestamp).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                ))}
                {metrics.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No metrics recorded
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
