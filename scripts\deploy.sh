#!/bin/bash

# Spaza Smart Order Deployment Script
# This script handles deployment to various environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="spaza-smart-order"
ENVIRONMENTS=("development" "staging" "production")
DEFAULT_ENVIRONMENT="development"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_help() {
    echo "Spaza Smart Order Deployment Script"
    echo ""
    echo "Usage: $0 [OPTIONS] [ENVIRONMENT]"
    echo ""
    echo "ENVIRONMENT:"
    echo "  development  Deploy to development environment (default)"
    echo "  staging      Deploy to staging environment"
    echo "  production   Deploy to production environment"
    echo ""
    echo "OPTIONS:"
    echo "  -h, --help     Show this help message"
    echo "  -v, --verify   Verify deployment after completion"
    echo "  -b, --build    Force rebuild before deployment"
    echo "  -t, --test     Run tests before deployment"
    echo "  --skip-checks  Skip pre-deployment checks"
    echo ""
    echo "Examples:"
    echo "  $0 production                    # Deploy to production"
    echo "  $0 --test --verify staging       # Test, deploy to staging, then verify"
    echo "  $0 --build production            # Force rebuild and deploy to production"
}

check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js 18 or later."
        exit 1
    fi
    
    # Check Node.js version
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js version 18 or later is required. Current version: $(node --version)"
        exit 1
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed."
        exit 1
    fi
    
    # Check if Vercel CLI is installed (for Vercel deployments)
    if ! command -v vercel &> /dev/null; then
        log_warning "Vercel CLI is not installed. Installing..."
        npm install -g vercel@latest
    fi
    
    log_success "All dependencies are available"
}

check_environment() {
    local env=$1
    log_info "Checking environment configuration for $env..."
    
    # Check if .env file exists for the environment
    if [ "$env" = "production" ]; then
        ENV_FILE=".env.production"
    elif [ "$env" = "staging" ]; then
        ENV_FILE=".env.staging"
    else
        ENV_FILE=".env.local"
    fi
    
    if [ ! -f "$ENV_FILE" ]; then
        log_warning "Environment file $ENV_FILE not found. Using .env.example as template."
        if [ -f ".env.example" ]; then
            cp .env.example "$ENV_FILE"
            log_warning "Please update $ENV_FILE with your environment-specific values."
        fi
    fi
    
    # Check required environment variables
    REQUIRED_VARS=("NEXT_PUBLIC_CONVEX_URL" "CONVEX_DEPLOYMENT")
    
    source "$ENV_FILE" 2>/dev/null || true
    
    for var in "${REQUIRED_VARS[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "Required environment variable $var is not set in $ENV_FILE"
            exit 1
        fi
    done
    
    log_success "Environment configuration is valid"
}

run_tests() {
    log_info "Running tests..."
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        log_info "Installing dependencies..."
        npm ci
    fi
    
    # Run linting
    log_info "Running linter..."
    npm run lint
    
    # Run type checking
    log_info "Running type check..."
    npm run type-check || true  # Don't fail on type errors for now
    
    # Run unit tests (if they exist)
    if npm run | grep -q "test"; then
        log_info "Running unit tests..."
        npm run test
    else
        log_warning "No test script found in package.json"
    fi
    
    log_success "All tests passed"
}

build_application() {
    log_info "Building application..."
    
    # Install dependencies
    log_info "Installing dependencies..."
    npm ci
    
    # Build the application
    log_info "Building Next.js application..."
    npm run build
    
    log_success "Application built successfully"
}

deploy_to_vercel() {
    local env=$1
    log_info "Deploying to Vercel ($env)..."
    
    if [ "$env" = "production" ]; then
        vercel --prod --yes
    else
        vercel --yes
    fi
    
    log_success "Deployed to Vercel"
}

deploy_with_docker() {
    local env=$1
    log_info "Deploying with Docker ($env)..."
    
    # Build Docker image
    log_info "Building Docker image..."
    docker build -t "$PROJECT_NAME:$env" .
    
    # Run container (this is a simple example)
    log_info "Starting container..."
    docker run -d --name "$PROJECT_NAME-$env" -p 3000:3000 "$PROJECT_NAME:$env"
    
    log_success "Docker deployment completed"
}

verify_deployment() {
    local env=$1
    log_info "Verifying deployment..."
    
    # Determine the URL based on environment
    if [ "$env" = "production" ]; then
        URL="https://spaza-smart-order.vercel.app"
    elif [ "$env" = "staging" ]; then
        URL="https://spaza-smart-order-staging.vercel.app"
    else
        URL="http://localhost:3000"
    fi
    
    # Wait a bit for deployment to be ready
    sleep 10
    
    # Check health endpoint
    log_info "Checking health endpoint: $URL/api/health"
    if curl -f -s "$URL/api/health" > /dev/null; then
        log_success "Health check passed"
    else
        log_error "Health check failed"
        exit 1
    fi
    
    # Check main page
    log_info "Checking main page: $URL"
    if curl -f -s "$URL" > /dev/null; then
        log_success "Main page is accessible"
    else
        log_error "Main page is not accessible"
        exit 1
    fi
    
    log_success "Deployment verification completed"
}

# Parse command line arguments
ENVIRONMENT="$DEFAULT_ENVIRONMENT"
RUN_TESTS=false
VERIFY_DEPLOYMENT=false
FORCE_BUILD=false
SKIP_CHECKS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -t|--test)
            RUN_TESTS=true
            shift
            ;;
        -v|--verify)
            VERIFY_DEPLOYMENT=true
            shift
            ;;
        -b|--build)
            FORCE_BUILD=true
            shift
            ;;
        --skip-checks)
            SKIP_CHECKS=true
            shift
            ;;
        development|staging|production)
            ENVIRONMENT=$1
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! " ${ENVIRONMENTS[@]} " =~ " ${ENVIRONMENT} " ]]; then
    log_error "Invalid environment: $ENVIRONMENT"
    log_error "Valid environments: ${ENVIRONMENTS[*]}"
    exit 1
fi

# Main deployment process
log_info "Starting deployment to $ENVIRONMENT environment"

# Pre-deployment checks
if [ "$SKIP_CHECKS" = false ]; then
    check_dependencies
    check_environment "$ENVIRONMENT"
fi

# Run tests if requested
if [ "$RUN_TESTS" = true ]; then
    run_tests
fi

# Build application if requested or if it's production
if [ "$FORCE_BUILD" = true ] || [ "$ENVIRONMENT" = "production" ]; then
    build_application
fi

# Deploy based on environment
case $ENVIRONMENT in
    production|staging)
        deploy_to_vercel "$ENVIRONMENT"
        ;;
    development)
        log_info "For development, starting local server..."
        npm run dev &
        DEV_PID=$!
        log_info "Development server started with PID: $DEV_PID"
        ;;
esac

# Verify deployment if requested
if [ "$VERIFY_DEPLOYMENT" = true ]; then
    verify_deployment "$ENVIRONMENT"
fi

log_success "Deployment to $ENVIRONMENT completed successfully!"

# Cleanup for development
if [ "$ENVIRONMENT" = "development" ] && [ ! -z "$DEV_PID" ]; then
    log_info "Press Ctrl+C to stop the development server"
    wait $DEV_PID
fi
