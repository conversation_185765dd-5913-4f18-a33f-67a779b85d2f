"use client"

import { createContext, use<PERSON>ontext, useEffect, useState, ReactNode } from "react"
import { generateCSRFToken, validateCSRFToken, apiRateLimiter } from "@/lib/validation"
import { captureError } from "@/lib/error-tracking"

interface SecurityContextType {
  csrfToken: string
  isRateLimited: (identifier: string) => boolean
  checkRateLimit: (identifier: string) => { allowed: boolean; remaining: number; resetTime: number }
  reportSecurityEvent: (event: SecurityEvent) => void
}

interface SecurityEvent {
  type: "rate_limit_exceeded" | "csrf_violation" | "suspicious_activity" | "xss_attempt"
  details: Record<string, any>
  severity: "low" | "medium" | "high" | "critical"
}

const SecurityContext = createContext<SecurityContextType | undefined>(undefined)

export function SecurityProvider({ children }: { children: ReactNode }) {
  const [csrfToken, setCsrfToken] = useState("")

  useEffect(() => {
    // Generate CSRF token on mount
    const token = generateCSRFToken()
    setCsrfToken(token)
    
    // Store in session storage for validation
    sessionStorage.setItem("csrf_token", token)

    // Set up security monitoring
    setupSecurityMonitoring()

    // Cleanup rate limiters periodically
    const cleanupInterval = setInterval(() => {
      apiRateLimiter.cleanup()
    }, 5 * 60 * 1000) // Every 5 minutes

    return () => {
      clearInterval(cleanupInterval)
    }
  }, [])

  const setupSecurityMonitoring = () => {
    // Monitor for potential XSS attempts
    const originalInnerHTML = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML')
    if (originalInnerHTML) {
      Object.defineProperty(Element.prototype, 'innerHTML', {
        set: function(value: string) {
          // Check for suspicious patterns
          const suspiciousPatterns = [
            /<script/i,
            /javascript:/i,
            /on\w+\s*=/i,
            /<iframe/i,
            /<object/i,
            /<embed/i,
          ]

          for (const pattern of suspiciousPatterns) {
            if (pattern.test(value)) {
              reportSecurityEvent({
                type: "xss_attempt",
                details: {
                  content: value.substring(0, 200), // First 200 chars
                  element: this.tagName,
                  url: window.location.href,
                },
                severity: "high",
              })
              
              // Sanitize the content
              value = value.replace(/<script.*?<\/script>/gi, '')
                          .replace(/javascript:/gi, '')
                          .replace(/on\w+\s*=/gi, '')
              break
            }
          }

          originalInnerHTML.set?.call(this, value)
        },
        get: originalInnerHTML.get,
        configurable: true,
      })
    }

    // Monitor for suspicious form submissions
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement
      const formData = new FormData(form)
      
      // Check for CSRF token
      const submittedToken = formData.get('csrf_token') as string
      const storedToken = sessionStorage.getItem('csrf_token')
      
      if (!validateCSRFToken(submittedToken, storedToken || '')) {
        event.preventDefault()
        reportSecurityEvent({
          type: "csrf_violation",
          details: {
            form: form.action || form.id || 'unknown',
            url: window.location.href,
          },
          severity: "high",
        })
      }
    })

    // Monitor for suspicious navigation patterns
    let navigationCount = 0
    const navigationStartTime = Date.now()
    
    const originalPushState = history.pushState
    const originalReplaceState = history.replaceState
    
    history.pushState = function(...args) {
      navigationCount++
      
      // Check for rapid navigation (potential bot behavior)
      const timeSinceStart = Date.now() - navigationStartTime
      if (navigationCount > 50 && timeSinceStart < 60000) { // 50 navigations in 1 minute
        reportSecurityEvent({
          type: "suspicious_activity",
          details: {
            type: "rapid_navigation",
            count: navigationCount,
            timeWindow: timeSinceStart,
          },
          severity: "medium",
        })
      }
      
      return originalPushState.apply(this, args)
    }
    
    history.replaceState = function(...args) {
      navigationCount++
      return originalReplaceState.apply(this, args)
    }
  }

  const isRateLimited = (identifier: string): boolean => {
    return !apiRateLimiter.isAllowed(identifier)
  }

  const checkRateLimit = (identifier: string) => {
    const allowed = apiRateLimiter.isAllowed(identifier)
    const remaining = apiRateLimiter.getRemainingRequests(identifier)
    const resetTime = apiRateLimiter.getResetTime(identifier)

    if (!allowed) {
      reportSecurityEvent({
        type: "rate_limit_exceeded",
        details: {
          identifier,
          remaining,
          resetTime,
        },
        severity: "medium",
      })
    }

    return { allowed, remaining, resetTime }
  }

  const reportSecurityEvent = (event: SecurityEvent) => {
    // Log security event
    console.warn("Security Event:", event)

    // Capture as error for monitoring
    captureError({
      message: `Security Event: ${event.type}`,
      severity: event.severity === "critical" ? "critical" : 
                event.severity === "high" ? "high" : "medium",
      category: "auth",
      metadata: {
        securityEvent: event.type,
        ...event.details,
      },
    })

    // In production, you might want to send this to a security monitoring service
    if (process.env.NODE_ENV === "production") {
      // Send to security monitoring service
      fetch("/api/security/report", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": csrfToken,
        },
        body: JSON.stringify(event),
      }).catch(error => {
        console.error("Failed to report security event:", error)
      })
    }
  }

  const value: SecurityContextType = {
    csrfToken,
    isRateLimited,
    checkRateLimit,
    reportSecurityEvent,
  }

  return (
    <SecurityContext.Provider value={value}>
      {children}
    </SecurityContext.Provider>
  )
}

export function useSecurity() {
  const context = useContext(SecurityContext)
  if (context === undefined) {
    throw new Error("useSecurity must be used within a SecurityProvider")
  }
  return context
}

// HOC for protecting components with rate limiting
export function withRateLimit<P extends object>(
  Component: React.ComponentType<P>,
  identifier: string
) {
  return function RateLimitedComponent(props: P) {
    const { checkRateLimit } = useSecurity()
    const [isBlocked, setIsBlocked] = useState(false)
    const [resetTime, setResetTime] = useState(0)

    useEffect(() => {
      const { allowed, resetTime: reset } = checkRateLimit(identifier)
      setIsBlocked(!allowed)
      setResetTime(reset)
    }, [identifier, checkRateLimit])

    if (isBlocked) {
      const timeUntilReset = Math.max(0, resetTime - Date.now())
      const minutesUntilReset = Math.ceil(timeUntilReset / 60000)

      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg text-center">
          <h3 className="text-lg font-semibold text-red-800 mb-2">
            Rate Limit Exceeded
          </h3>
          <p className="text-red-600">
            Too many requests. Please try again in {minutesUntilReset} minute{minutesUntilReset !== 1 ? 's' : ''}.
          </p>
        </div>
      )
    }

    return <Component {...props} />
  }
}

// Hook for secure form handling
export function useSecureForm() {
  const { csrfToken, reportSecurityEvent } = useSecurity()

  const createSecureFormData = (data: Record<string, any>) => {
    return {
      ...data,
      csrf_token: csrfToken,
    }
  }

  const validateFormSubmission = (formData: FormData): boolean => {
    const submittedToken = formData.get('csrf_token') as string
    const storedToken = sessionStorage.getItem('csrf_token')

    if (!validateCSRFToken(submittedToken, storedToken || '')) {
      reportSecurityEvent({
        type: "csrf_violation",
        details: {
          submittedToken: submittedToken ? "present" : "missing",
          storedToken: storedToken ? "present" : "missing",
        },
        severity: "high",
      })
      return false
    }

    return true
  }

  return {
    csrfToken,
    createSecureFormData,
    validateFormSubmission,
  }
}
