"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { 
  Smartphone, 
  Bell, 
  Download, 
  Wifi, 
  WifiOff, 
  Settings,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react"
import { usePWAInstall } from "@/components/pwa/install-prompt"
import { usePushNotifications } from "@/lib/push-notifications"
import { toast } from "sonner"

export default function PWASettingsPage() {
  const [isOnline, setIsOnline] = useState(true)
  const [storageUsage, setStorageUsage] = useState<number | null>(null)
  const [storageQuota, setStorageQuota] = useState<number | null>(null)
  
  const { isInstalled, isInstallable, install } = usePWAInstall()
  const { 
    isSupported: notificationsSupported, 
    isSubscribed, 
    permission, 
    isLoading, 
    subscribe, 
    unsubscribe 
  } = usePushNotifications()

  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    setIsOnline(navigator.onLine)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Get storage usage
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      navigator.storage.estimate().then(estimate => {
        setStorageUsage(estimate.usage || 0)
        setStorageQuota(estimate.quota || 0)
      })
    }

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const handleInstall = async () => {
    const success = await install()
    if (success) {
      toast.success("App installed successfully!")
    } else {
      toast.error("Failed to install app")
    }
  }

  const handleNotificationToggle = async (enabled: boolean) => {
    if (enabled) {
      const success = await subscribe()
      if (success) {
        toast.success("Notifications enabled!")
      } else {
        toast.error("Failed to enable notifications")
      }
    } else {
      const success = await unsubscribe()
      if (success) {
        toast.success("Notifications disabled")
      } else {
        toast.error("Failed to disable notifications")
      }
    }
  }

  const clearCache = async () => {
    if ('caches' in window) {
      try {
        const cacheNames = await caches.keys()
        await Promise.all(cacheNames.map(name => caches.delete(name)))
        toast.success("Cache cleared successfully")
        
        // Refresh storage usage
        if ('storage' in navigator && 'estimate' in navigator.storage) {
          const estimate = await navigator.storage.estimate()
          setStorageUsage(estimate.usage || 0)
          setStorageQuota(estimate.quota || 0)
        }
      } catch (error) {
        toast.error("Failed to clear cache")
      }
    }
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getPermissionBadge = (permission: NotificationPermission) => {
    switch (permission) {
      case 'granted':
        return <Badge variant="default" className="bg-green-100 text-green-800">Granted</Badge>
      case 'denied':
        return <Badge variant="destructive">Denied</Badge>
      default:
        return <Badge variant="secondary">Not Set</Badge>
    }
  }

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="w-5 h-5 text-green-600" />
    ) : (
      <XCircle className="w-5 h-5 text-red-600" />
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">PWA Settings</h1>
          <p className="text-gray-600">
            Manage your Progressive Web App experience
          </p>
        </div>

        {/* Connection Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {isOnline ? <Wifi className="w-5 h-5" /> : <WifiOff className="w-5 h-5" />}
              Connection Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">
                You are currently {isOnline ? 'online' : 'offline'}
              </span>
              <Badge variant={isOnline ? "default" : "destructive"}>
                {isOnline ? 'Online' : 'Offline'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* App Installation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="w-5 h-5" />
              App Installation
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Install Status</p>
                <p className="text-sm text-gray-600">
                  {isInstalled ? 'App is installed' : 'App is not installed'}
                </p>
              </div>
              {getStatusIcon(isInstalled)}
            </div>

            {!isInstalled && isInstallable && (
              <Button onClick={handleInstall} className="w-full">
                <Download className="w-4 h-4 mr-2" />
                Install App
              </Button>
            )}

            {!isInstalled && !isInstallable && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-sm text-blue-700">
                  To install this app, use your browser's "Add to Home Screen" option.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Push Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="w-5 h-5" />
              Push Notifications
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Notification Support</p>
                <p className="text-sm text-gray-600">
                  {notificationsSupported ? 'Supported' : 'Not supported'}
                </p>
              </div>
              {getStatusIcon(notificationsSupported)}
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Permission Status</p>
                <p className="text-sm text-gray-600">Current permission level</p>
              </div>
              {getPermissionBadge(permission)}
            </div>

            {notificationsSupported && (
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Enable Notifications</p>
                  <p className="text-sm text-gray-600">
                    Get notified about order updates and promotions
                  </p>
                </div>
                <Switch
                  checked={isSubscribed}
                  onCheckedChange={handleNotificationToggle}
                  disabled={isLoading || permission === 'denied'}
                />
              </div>
            )}

            {permission === 'denied' && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <AlertCircle className="w-4 h-4 text-yellow-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800">
                      Notifications Blocked
                    </p>
                    <p className="text-sm text-yellow-700">
                      To enable notifications, please allow them in your browser settings.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Storage & Cache */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Storage & Cache
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {storageUsage !== null && storageQuota !== null && (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <p className="font-medium">Storage Usage</p>
                  <span className="text-sm text-gray-600">
                    {formatBytes(storageUsage)} / {formatBytes(storageQuota)}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ 
                      width: `${Math.min((storageUsage / storageQuota) * 100, 100)}%` 
                    }}
                  />
                </div>
              </div>
            )}

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Clear Cache</p>
                <p className="text-sm text-gray-600">
                  Remove cached data to free up space
                </p>
              </div>
              <Button variant="outline" onClick={clearCache}>
                Clear Cache
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* PWA Features */}
        <Card>
          <CardHeader>
            <CardTitle>PWA Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                {getStatusIcon(true)}
                <span className="text-sm">Offline Support</span>
              </div>
              
              <div className="flex items-center gap-3">
                {getStatusIcon(isInstalled)}
                <span className="text-sm">Home Screen Install</span>
              </div>
              
              <div className="flex items-center gap-3">
                {getStatusIcon(notificationsSupported)}
                <span className="text-sm">Push Notifications</span>
              </div>
              
              <div className="flex items-center gap-3">
                {getStatusIcon(true)}
                <span className="text-sm">Background Sync</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
