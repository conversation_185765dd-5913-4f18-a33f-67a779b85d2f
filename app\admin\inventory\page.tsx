"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { RealTimeInventory } from "@/components/admin/real-time-inventory"
import { RealTimeOrders } from "@/components/admin/real-time-orders"
import {
  ArrowLeft,
  Package,
  ShoppingCart,
} from "lucide-react"

export default function InventoryManagement() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState<"inventory" | "orders">("inventory")



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button variant="ghost" size="sm" onClick={() => router.back()}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Real-time Management</h1>
                <p className="text-sm text-gray-600">Live inventory and order tracking</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="p-4">
        {/* Tab Navigation */}
        <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg w-fit">
          <Button
            variant={activeTab === "inventory" ? "default" : "ghost"}
            size="sm"
            onClick={() => setActiveTab("inventory")}
            className="flex items-center gap-2"
          >
            <Package className="h-4 w-4" />
            Inventory
          </Button>
          <Button
            variant={activeTab === "orders" ? "default" : "ghost"}
            size="sm"
            onClick={() => setActiveTab("orders")}
            className="flex items-center gap-2"
          >
            <ShoppingCart className="h-4 w-4" />
            Orders
          </Button>
        </div>

        {/* Content */}
        {activeTab === "inventory" && <RealTimeInventory />}
        {activeTab === "orders" && <RealTimeOrders />}
      </div>
    </div>
  )
}
