{"version": 2, "name": "spaza-smart-order", "alias": ["spaza-smart-order.vercel.app"], "regions": ["cpt1"], "build": {"env": {"NODE_ENV": "production"}}, "env": {"NODE_ENV": "production", "NEXT_PUBLIC_APP_URL": "https://spaza-smart-order.vercel.app"}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}, "app/api/webhooks/**/*.ts": {"maxDuration": 60}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, s-maxage=60, stale-while-revalidate=300"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/manifest.json", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}, {"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": false}, {"source": "/dashboard", "destination": "/admin", "permanent": false}], "rewrites": [{"source": "/api/health", "destination": "/api/health"}, {"source": "/sitemap.xml", "destination": "/api/sitemap"}, {"source": "/robots.txt", "destination": "/api/robots"}], "crons": [{"path": "/api/cron/cleanup", "schedule": "0 2 * * *"}, {"path": "/api/cron/analytics", "schedule": "0 1 * * *"}, {"path": "/api/cron/health-check", "schedule": "*/5 * * * *"}]}