"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"

export default function RootPage() {
  const router = useRouter()
  const { user, loading } = useAuth()

  useEffect(() => {
    // Only redirect if we're done loading
    if (loading) return
    
    // Add a small delay to ensure any redirects complete
    const timer = setTimeout(() => {
      if (user) {
        // User is authenticated, redirect to home
        router.push("/home")
      } else {
        // User is not authenticated, redirect to login
        router.push("/login")
      }
    }, 100)

    return () => clearTimeout(timer)
  }, [user, loading, router])

  // Show loading spinner while checking authentication
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="flex flex-col items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
        <p className="text-gray-600">Loading your shopping experience...</p>
      </div>
    </div>
  )
}


