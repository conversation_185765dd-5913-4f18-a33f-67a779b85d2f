"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { ArrowLeft, Package, Truck, CheckCircle, MapPin, Clock, Phone, MessageCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface TrackingStep {
  id: string
  title: string
  description: string
  timestamp: string
  status: "completed" | "current" | "pending"
  location?: string
}

interface OrderDetails {
  id: string
  orderNumber: string
  trackingNumber: string
  status: string
  estimatedDelivery: string
  deliveryAddress: string
  driverName?: string
  driverPhone?: string
  items: Array<{
    name: string
    quantity: number
    image: string
  }>
}

export default function TrackOrderPage() {
  const params = useParams()
  const router = useRouter()
  const orderId = params.orderId as string

  const [orderDetails] = useState<OrderDetails>({
    id: orderId,
    orderNumber: "ORD-2024-001",
    trackingNumber: "TRK123456789",
    status: "out-for-delivery",
    estimatedDelivery: "Today, 2:00 PM - 4:00 PM",
    deliveryAddress: "123 Main St, Johannesburg, 2001",
    driverName: "<PERSON>",
    driverPhone: "+27 82 123 4567",
    items: [
      {
        name: "White Star Maize Meal 5kg",
        quantity: 2,
        image: "/placeholder-1l6vi.png",
      },
      {
        name: "Coca-Cola Can 300ml",
        quantity: 3,
        image: "/refreshing-cola-can.png",
      },
    ],
  })

  const [trackingSteps] = useState<TrackingStep[]>([
    {
      id: "1",
      title: "Order Confirmed",
      description: "Your order has been confirmed and is being prepared",
      timestamp: "Today, 9:30 AM",
      status: "completed",
      location: "Spaza Smart Order Warehouse",
    },
    {
      id: "2",
      title: "Order Prepared",
      description: "Your items have been packed and ready for dispatch",
      timestamp: "Today, 11:15 AM",
      status: "completed",
      location: "Spaza Smart Order Warehouse",
    },
    {
      id: "3",
      title: "Out for Delivery",
      description: "Your order is on the way to your delivery address",
      timestamp: "Today, 1:45 PM",
      status: "current",
      location: "En route to Johannesburg",
    },
    {
      id: "4",
      title: "Delivered",
      description: "Order delivered successfully",
      timestamp: "Estimated: Today, 2:00 PM - 4:00 PM",
      status: "pending",
      location: orderDetails.deliveryAddress,
    },
  ])

  const getStepIcon = (status: TrackingStep["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-6 h-6 text-green-500" />
      case "current":
        return <Truck className="w-6 h-6 text-blue-600" />
      case "pending":
        return <Package className="w-6 h-6 text-gray-300" />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="flex items-center gap-3">
          <Button variant="ghost" size="sm" className="text-white hover:bg-blue-700 p-2" onClick={() => router.back()}>
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="text-lg font-semibold">Track Order</h1>
            <p className="text-blue-100 text-sm">{orderDetails.orderNumber}</p>
          </div>
        </div>
      </div>

      {/* Tracking Number */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="text-center">
          <p className="text-sm text-gray-600 mb-1">Tracking Number</p>
          <p className="text-lg font-mono font-semibold text-gray-900">{orderDetails.trackingNumber}</p>
        </div>
      </div>

      {/* Current Status */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center gap-3 mb-3">
          <Truck className="w-8 h-8 text-blue-600" />
          <div>
            <h2 className="font-semibold text-gray-900">Out for Delivery</h2>
            <p className="text-sm text-gray-600">Your order is on the way</p>
          </div>
        </div>
        <div className="bg-blue-50 rounded-lg p-3">
          <div className="flex items-center gap-2 mb-2">
            <Clock className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-900">Estimated Delivery</span>
          </div>
          <p className="text-blue-800 font-semibold">{orderDetails.estimatedDelivery}</p>
        </div>
      </div>

      {/* Driver Info */}
      {orderDetails.driverName && (
        <div className="bg-white border-b border-gray-200 p-4">
          <h3 className="font-semibold text-gray-900 mb-3">Your Driver</h3>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-lg">
                  {orderDetails.driverName
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </span>
              </div>
              <div>
                <p className="font-medium text-gray-900">{orderDetails.driverName}</p>
                <p className="text-sm text-gray-600">Delivery Driver</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button size="sm" variant="outline" className="p-2 bg-transparent">
                <Phone className="w-4 h-4" />
              </Button>
              <Button size="sm" variant="outline" className="p-2 bg-transparent">
                <MessageCircle className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Tracking Timeline */}
      <div className="bg-white p-4">
        <h3 className="font-semibold text-gray-900 mb-4">Order Timeline</h3>
        <div className="space-y-4">
          {trackingSteps.map((step, index) => (
            <div key={step.id} className="flex gap-4">
              <div className="flex flex-col items-center">
                {getStepIcon(step.status)}
                {index < trackingSteps.length - 1 && (
                  <div className={`w-0.5 h-8 mt-2 ${step.status === "completed" ? "bg-green-200" : "bg-gray-200"}`} />
                )}
              </div>
              <div className="flex-1 pb-4">
                <div className="flex items-center gap-2 mb-1">
                  <h4
                    className={`font-medium ${
                      step.status === "current"
                        ? "text-blue-600"
                        : step.status === "completed"
                          ? "text-gray-900"
                          : "text-gray-500"
                    }`}
                  >
                    {step.title}
                  </h4>
                  {step.status === "current" && <Badge className="bg-blue-100 text-blue-800 text-xs">Current</Badge>}
                </div>
                <p className={`text-sm mb-1 ${step.status === "pending" ? "text-gray-500" : "text-gray-600"}`}>
                  {step.description}
                </p>
                <p className="text-xs text-gray-500">{step.timestamp}</p>
                {step.location && (
                  <div className="flex items-center gap-1 mt-1">
                    <MapPin className="w-3 h-3 text-gray-400" />
                    <p className="text-xs text-gray-500">{step.location}</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Order Items */}
      <div className="bg-white border-t border-gray-200 p-4">
        <h3 className="font-semibold text-gray-900 mb-3">Order Items</h3>
        <div className="space-y-3">
          {orderDetails.items.map((item, index) => (
            <div key={index} className="flex items-center gap-3">
              <img
                src={item.image || "/placeholder.svg"}
                alt={item.name}
                className="w-12 h-12 object-cover rounded-md"
              />
              <div className="flex-1">
                <p className="font-medium text-sm text-gray-900">{item.name}</p>
                <p className="text-xs text-gray-500">Quantity: {item.quantity}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Delivery Address */}
      <div className="bg-white border-t border-gray-200 p-4">
        <h3 className="font-semibold text-gray-900 mb-2">Delivery Address</h3>
        <div className="flex items-start gap-2">
          <MapPin className="w-4 h-4 text-gray-500 mt-0.5" />
          <p className="text-sm text-gray-600">{orderDetails.deliveryAddress}</p>
        </div>
      </div>

      {/* Support Button */}
      <div className="p-4">
        <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white" onClick={() => router.push("/support")}>
          <Phone className="w-4 h-4 mr-2" />
          Contact Support
        </Button>
      </div>

      {/* Bottom padding */}
      <div className="h-4"></div>
    </div>
  )
}
