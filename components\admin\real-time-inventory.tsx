"use client"

import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Package,
  AlertTriangle,
  CheckCircle,
  Edit,
  Save,
  X,
  Plus,
  Minus
} from "lucide-react"
import { useState } from "react"

export function RealTimeInventory() {
  const [editingProduct, setEditingProduct] = useState<string | null>(null)
  const [stockUpdates, setStockUpdates] = useState<Record<string, number>>({})
  
  // Get all products with real-time updates
  const products = useQuery(api.products.list, { category: "all" })
  
  // Mutation to update product stock
  const updateProductStock = useMutation(api.products.updateStock)

  const handleEditStock = (productId: string, currentStock: number) => {
    setEditingProduct(productId)
    setStockUpdates(prev => ({ ...prev, [productId]: currentStock }))
  }

  const handleSaveStock = async (productId: string) => {
    const newStock = stockUpdates[productId]
    if (newStock !== undefined) {
      await updateProductStock({ id: productId as any, stockQuantity: newStock })
    }
    setEditingProduct(null)
    setStockUpdates(prev => {
      const updated = { ...prev }
      delete updated[productId]
      return updated
    })
  }

  const handleCancelEdit = (productId: string) => {
    setEditingProduct(null)
    setStockUpdates(prev => {
      const updated = { ...prev }
      delete updated[productId]
      return updated
    })
  }

  const adjustStock = (productId: string, adjustment: number) => {
    setStockUpdates(prev => ({
      ...prev,
      [productId]: (prev[productId] || 0) + adjustment
    }))
  }

  const getStockStatus = (stock: number, inStock: boolean) => {
    if (!inStock || stock === 0) {
      return { status: 'out', color: 'bg-red-100 text-red-800', icon: <AlertTriangle className="h-4 w-4" /> }
    } else if (stock < 10) {
      return { status: 'low', color: 'bg-yellow-100 text-yellow-800', icon: <AlertTriangle className="h-4 w-4" /> }
    } else {
      return { status: 'good', color: 'bg-green-100 text-green-800', icon: <CheckCircle className="h-4 w-4" /> }
    }
  }

  if (products === undefined) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const lowStockProducts = products.filter(p => p.stockQuantity && p.stockQuantity < 10)
  const outOfStockProducts = products.filter(p => !p.inStock || (p.stockQuantity && p.stockQuantity === 0))

  return (
    <div className="space-y-6">
      {/* Stock Alerts */}
      {(lowStockProducts.length > 0 || outOfStockProducts.length > 0) && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-800">
              <AlertTriangle className="h-5 w-5" />
              Stock Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {outOfStockProducts.length > 0 && (
                <div>
                  <h4 className="font-medium text-red-800">Out of Stock ({outOfStockProducts.length})</h4>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {outOfStockProducts.map(product => (
                      <Badge key={product._id} variant="destructive">
                        {product.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              {lowStockProducts.length > 0 && (
                <div>
                  <h4 className="font-medium text-yellow-800">Low Stock ({lowStockProducts.length})</h4>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {lowStockProducts.map(product => (
                      <Badge key={product._id} variant="secondary" className="bg-yellow-100 text-yellow-800">
                        {product.name} ({product.stockQuantity})
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Inventory List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Real-time Inventory
          </CardTitle>
        </CardHeader>
        <CardContent>
          {products.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No products found
            </div>
          ) : (
            <div className="space-y-4">
              {products.map((product) => {
                const stockStatus = getStockStatus(product.stockQuantity || 0, product.inStock)
                const isEditing = editingProduct === product._id
                const currentStock = isEditing ? (stockUpdates[product._id] ?? product.stockQuantity ?? 0) : (product.stockQuantity ?? 0)

                return (
                  <div
                    key={product._id}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <img 
                          src={product.image} 
                          alt={product.name}
                          className="w-12 h-12 object-cover rounded"
                        />
                        <div>
                          <h3 className="font-semibold">{product.name}</h3>
                          <p className="text-sm text-muted-foreground">{product.category}</p>
                        </div>
                      </div>
                      <Badge className={stockStatus.color}>
                        {stockStatus.icon}
                        <span className="ml-1 capitalize">{stockStatus.status}</span>
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <label className="text-sm font-medium">Price</label>
                        <p className="text-lg font-semibold">R{product.price.toFixed(2)}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Stock Quantity</label>
                        {isEditing ? (
                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => adjustStock(product._id, -1)}
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <Input
                              type="number"
                              value={currentStock}
                              onChange={(e) => setStockUpdates(prev => ({
                                ...prev,
                                [product._id]: parseInt(e.target.value) || 0
                              }))}
                              className="w-20 text-center"
                            />
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => adjustStock(product._id, 1)}
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>
                        ) : (
                          <p className="text-lg font-semibold">{currentStock}</p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium">Status</label>
                        <p className="text-sm">{product.inStock ? 'In Stock' : 'Out of Stock'}</p>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      {isEditing ? (
                        <>
                          <Button
                            size="sm"
                            onClick={() => handleSaveStock(product._id)}
                          >
                            <Save className="h-4 w-4 mr-1" />
                            Save
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleCancelEdit(product._id)}
                          >
                            <X className="h-4 w-4 mr-1" />
                            Cancel
                          </Button>
                        </>
                      ) : (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditStock(product._id, product.stockQuantity || 0)}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit Stock
                        </Button>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
