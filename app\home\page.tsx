"use client"

import { useState, useEffect } from "react"
import { Search, ShoppingCart, Bell, Filter } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import BottomNavigation from "@/components/bottom-navigation"
import { ProductCatalog } from "@/components/customer/product-catalog"
import { useConvexCart } from "@/hooks/use-convex-cart"
import { useAuth } from "@/contexts/auth-context"
import { useRouter } from "next/navigation"

interface Category {
  id: string
  name: string
  icon: string
}

const categories: Category[] = [
  { id: "all", name: "All", icon: "🛒" },
  { id: "groceries", name: "Groceries", icon: "🥬" },
  { id: "beverages", name: "Beverages", icon: "🥤" },
  { id: "snacks", name: "Snacks", icon: "🍿" },
  { id: "dairy", name: "Dairy", icon: "🥛" },
  { id: "meat", name: "Meat", icon: "🥩" },
]

export default function HomePage() {
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [showNotifications, setShowNotifications] = useState(false)
  
  // Get real-time cart count (only if user is authenticated)
  const { cartItems } = useConvexCart(user?._id || "")
  const cartCount = cartItems.length
  
  // Handle search input changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }
  
  // Handle category selection
  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category)
  }

  const handleShopNow = () => {
    // Scroll to products section
    document.getElementById('products')?.scrollIntoView({ behavior: 'smooth' })
  }

  // Redirect to login if not authenticated
  useEffect(() => {
    let timer: NodeJS.Timeout

    if (!authLoading && !user) {
      // Add a small delay to prevent rapid redirects
      timer = setTimeout(() => {
        router.push("/login")
      }, 100)
    }

    return () => {
      if (timer) clearTimeout(timer)
    }
  }, [user, authLoading, router])

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-gray-600">Loading your shopping experience...</p>
        </div>
      </div>
    )
  }

  // If not authenticated, show nothing (will be redirected by useEffect)
  if (!user) {
    return null
  }

  const handleNotifications = () => {
    setShowNotifications(!showNotifications)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with search and cart */}
      <header className="sticky top-0 z-10 bg-white shadow-sm p-4">
        <div className="container mx-auto">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-xl font-bold text-gray-900">Spaza Shop</h1>
            <div className="flex items-center space-x-2">
              <button
                className="p-2 rounded-full hover:bg-gray-100 relative"
                onClick={handleNotifications}
              >
                <Bell className="w-5 h-5 text-gray-700" />
              </button>
              <button
                className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 relative"
                onClick={() => router.push("/cart")}
              >
                <ShoppingCart className="w-5 h-5 text-gray-700" />
                {cartCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {cartCount}
                  </span>
                )}
              </button>
            </div>
          </div>

          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search products..."
              className="pl-10 pr-10 w-full"
              value={searchQuery}
              onChange={handleSearchChange}
            />
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 h-5 w-5"
              onClick={() => { /* Filter functionality */ }}
            >
              <Filter className="w-4 h-4 text-gray-600" />
            </Button>
          </div>

          {/* Category Filter */}
          <div className="mt-3 flex space-x-2 overflow-x-auto pb-2">
            {categories.map((category) => (
              <button
                key={category.id}
                className={`px-3 py-1.5 text-sm rounded-full whitespace-nowrap ${
                  selectedCategory === category.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                onClick={() => handleCategorySelect(category.id)}
              >
                {category.icon} {category.name}
              </button>
            ))}
          </div>
        </div>
      </header>

      {/* Notifications Dropdown */}
      {showNotifications && (
        <div className="absolute right-4 mt-2 w-64 bg-white rounded-md shadow-lg py-1 z-20">
          <div className="px-4 py-2 text-sm text-gray-700">No new notifications</div>
        </div>
      )}

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-10 px-4">
        <div className="container mx-auto text-center">
          <h2 className="text-2xl font-bold mb-2">Welcome to Spaza Shop</h2>
          <p className="mb-4">Get your favorite products delivered to your doorstep</p>
          <Button
            variant="secondary"
            size="sm"
            className="bg-white text-blue-600 hover:bg-gray-100"
            onClick={handleShopNow}
          >
            Shop Now
          </Button>
        </div>
      </div>

      {/* Categories */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex gap-2 overflow-x-auto pb-2">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              className={`flex-shrink-0 ${
                selectedCategory === category.id
                  ? "bg-blue-600 text-white"
                  : "bg-white text-gray-700 border-gray-300"
              }`}
              onClick={() => setSelectedCategory(category.id)}
            >
              <span className="mr-1">{category.icon}</span>
              {category.name}
            </Button>
          ))}
        </div>
      </div>

      {/* Real-time Product Catalog */}
      <main className="flex-1 pb-16">
        <ProductCatalog userId={user._id} />
      </main>

      {/* Bottom Navigation */}
      <BottomNavigation cartCount={cartCount} />

      {/* Bottom padding to account for fixed navigation */}
      <div className="h-20"></div>
    </div>
  )
}
