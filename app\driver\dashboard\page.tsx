"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, Navigation, Package, Clock, Phone, MapPin, CheckCircle, AlertTriangle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function DriverDashboard() {
  const router = useRouter()
  const [driverStatus, setDriverStatus] = useState<"online" | "offline" | "busy">("online")

  const activeDeliveries = [
    {
      id: "del-001",
      orderNumber: "ORD-2024-001",
      customerName: "<PERSON>",
      address: "123 Main St, Soweto",
      phone: "+27 82 123 4567",
      items: 3,
      priority: "high",
      estimatedTime: "15 mins",
      distance: "2.3 km",
      status: "picked-up",
    },
    {
      id: "del-002",
      orderNumber: "ORD-2024-002",
      customerName: "<PERSON>ba",
      address: "456 Oak Ave, Sandton",
      phone: "+27 83 987 6543",
      items: 5,
      priority: "normal",
      estimatedTime: "25 mins",
      distance: "4.1 km",
      status: "assigned",
    },
  ]

  const todayStats = {
    deliveriesCompleted: 12,
    totalEarnings: 480,
    averageRating: 4.8,
    totalDistance: 45.2,
  }

  const toggleStatus = () => {
    const statuses: Array<"online" | "offline" | "busy"> = ["online", "offline", "busy"]
    const currentIndex = statuses.indexOf(driverStatus)
    const nextIndex = (currentIndex + 1) % statuses.length
    setDriverStatus(statuses[nextIndex])
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-blue-700 p-2"
              onClick={() => router.back()}
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <div>
              <h1 className="text-lg font-semibold">Driver Dashboard</h1>
              <p className="text-blue-100 text-sm">John Mthembu</p>
            </div>
          </div>
          <Button
            onClick={toggleStatus}
            className={`px-4 py-2 rounded-full text-sm font-medium ${
              driverStatus === "online"
                ? "bg-green-500 hover:bg-green-600"
                : driverStatus === "busy"
                  ? "bg-yellow-500 hover:bg-yellow-600"
                  : "bg-red-500 hover:bg-red-600"
            }`}
          >
            {driverStatus === "online" ? "Online" : driverStatus === "busy" ? "Busy" : "Offline"}
          </Button>
        </div>
      </div>

      <div className="p-4 space-y-4">
        {/* Today's Stats */}
        <Card>
          <CardHeader>
            <CardTitle>Today's Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <p className="text-2xl font-bold text-blue-600">{todayStats.deliveriesCompleted}</p>
                <p className="text-sm text-gray-600">Deliveries</p>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <p className="text-2xl font-bold text-green-600">R {todayStats.totalEarnings}</p>
                <p className="text-sm text-gray-600">Earnings</p>
              </div>
              <div className="text-center p-3 bg-yellow-50 rounded-lg">
                <p className="text-2xl font-bold text-yellow-600">{todayStats.averageRating}</p>
                <p className="text-sm text-gray-600">Rating</p>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <p className="text-2xl font-bold text-purple-600">{todayStats.totalDistance} km</p>
                <p className="text-sm text-gray-600">Distance</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Active Deliveries */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="w-5 h-5 text-blue-600" />
              Active Deliveries ({activeDeliveries.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {activeDeliveries.map((delivery) => (
              <div key={delivery.id} className="p-4 border rounded-lg">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="font-semibold text-gray-900">{delivery.orderNumber}</h3>
                    <p className="text-sm text-gray-600">{delivery.customerName}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    {delivery.priority === "high" && (
                      <Badge variant="destructive" className="bg-red-100 text-red-800">
                        <AlertTriangle className="w-3 h-3 mr-1" />
                        High Priority
                      </Badge>
                    )}
                    <Badge
                      variant="secondary"
                      className={
                        delivery.status === "picked-up" ? "bg-blue-100 text-blue-800" : "bg-yellow-100 text-yellow-800"
                      }
                    >
                      {delivery.status === "picked-up" ? "In Transit" : "Assigned"}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600">{delivery.address}</span>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span>{delivery.items} items</span>
                    <span>{delivery.distance}</span>
                    <span>ETA: {delivery.estimatedTime}</span>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="flex-1 bg-transparent">
                    <Navigation className="w-4 h-4 mr-2" />
                    Navigate
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1 bg-transparent">
                    <Phone className="w-4 h-4 mr-2" />
                    Call Customer
                  </Button>
                  {delivery.status === "picked-up" ? (
                    <Button size="sm" className="flex-1 bg-green-600 hover:bg-green-700">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Mark Delivered
                    </Button>
                  ) : (
                    <Button size="sm" className="flex-1 bg-blue-600 hover:bg-blue-700">
                      <Package className="w-4 h-4 mr-2" />
                      Pick Up
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              <Button variant="outline" className="h-16 flex flex-col gap-1 bg-transparent">
                <Clock className="w-5 h-5" />
                <span className="text-sm">Break Time</span>
              </Button>
              <Button variant="outline" className="h-16 flex flex-col gap-1 bg-transparent">
                <AlertTriangle className="w-5 h-5" />
                <span className="text-sm">Report Issue</span>
              </Button>
              <Button variant="outline" className="h-16 flex flex-col gap-1 bg-transparent">
                <Navigation className="w-5 h-5" />
                <span className="text-sm">Update Location</span>
              </Button>
              <Button variant="outline" className="h-16 flex flex-col gap-1 bg-transparent">
                <Phone className="w-5 h-5" />
                <span className="text-sm">Contact Support</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
