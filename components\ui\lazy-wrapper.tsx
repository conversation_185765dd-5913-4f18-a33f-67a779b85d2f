"use client"

import { useState, useRef, useEffect, ReactNode, Suspense } from "react"
import { createIntersectionObserver } from "@/lib/cache"

interface LazyWrapperProps {
  children: ReactNode
  fallback?: ReactNode
  rootMargin?: string
  threshold?: number
  className?: string
  once?: boolean
}

export function LazyWrapper({
  children,
  fallback = <div className="animate-pulse bg-gray-200 rounded h-32" />,
  rootMargin = "100px",
  threshold = 0.1,
  className,
  once = true,
}: LazyWrapperProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [hasBeenVisible, setHasBeenVisible] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = createIntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true)
            if (once) {
              setHasBeenVisible(true)
              observer?.unobserve(entry.target)
            }
          } else if (!once) {
            setIsVisible(false)
          }
        })
      },
      { rootMargin, threshold }
    )

    if (observer && ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      if (observer && ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [rootMargin, threshold, once])

  const shouldRender = isVisible || hasBeenVisible

  return (
    <div ref={ref} className={className}>
      {shouldRender ? (
        <Suspense fallback={fallback}>
          {children}
        </Suspense>
      ) : (
        fallback
      )}
    </div>
  )
}

// Specific lazy components for common use cases
interface LazyProductGridProps {
  children: ReactNode
  itemCount?: number
}

export function LazyProductGrid({ children, itemCount = 8 }: LazyProductGridProps) {
  const skeletonItems = Array.from({ length: itemCount }, (_, i) => (
    <div key={i} className="space-y-3">
      <div className="aspect-square bg-gray-200 rounded-lg animate-pulse" />
      <div className="space-y-2">
        <div className="h-4 bg-gray-200 rounded animate-pulse" />
        <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse" />
        <div className="h-6 bg-gray-200 rounded w-1/2 animate-pulse" />
      </div>
    </div>
  ))

  return (
    <LazyWrapper
      fallback={
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {skeletonItems}
        </div>
      }
    >
      {children}
    </LazyWrapper>
  )
}

interface LazyChartProps {
  children: ReactNode
  height?: number
}

export function LazyChart({ children, height = 300 }: LazyChartProps) {
  return (
    <LazyWrapper
      fallback={
        <div 
          className="bg-gray-100 rounded-lg animate-pulse flex items-center justify-center"
          style={{ height }}
        >
          <div className="text-gray-500">Loading chart...</div>
        </div>
      }
    >
      {children}
    </LazyWrapper>
  )
}

interface LazyTableProps {
  children: ReactNode
  rows?: number
  columns?: number
}

export function LazyTable({ children, rows = 5, columns = 4 }: LazyTableProps) {
  const skeletonRows = Array.from({ length: rows }, (_, i) => (
    <tr key={i}>
      {Array.from({ length: columns }, (_, j) => (
        <td key={j} className="p-4">
          <div className="h-4 bg-gray-200 rounded animate-pulse" />
        </td>
      ))}
    </tr>
  ))

  return (
    <LazyWrapper
      fallback={
        <div className="border rounded-lg overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                {Array.from({ length: columns }, (_, i) => (
                  <th key={i} className="p-4">
                    <div className="h-4 bg-gray-300 rounded animate-pulse" />
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {skeletonRows}
            </tbody>
          </table>
        </div>
      }
    >
      {children}
    </LazyWrapper>
  )
}

// Hook for lazy loading with custom logic
export function useLazyLoading(
  options: {
    rootMargin?: string
    threshold?: number
    once?: boolean
  } = {}
) {
  const [isVisible, setIsVisible] = useState(false)
  const [hasBeenVisible, setHasBeenVisible] = useState(false)
  const ref = useRef<HTMLElement>(null)

  const { rootMargin = "100px", threshold = 0.1, once = true } = options

  useEffect(() => {
    const observer = createIntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true)
            if (once) {
              setHasBeenVisible(true)
              observer?.unobserve(entry.target)
            }
          } else if (!once) {
            setIsVisible(false)
          }
        })
      },
      { rootMargin, threshold }
    )

    if (observer && ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      if (observer && ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [rootMargin, threshold, once])

  return {
    ref,
    isVisible: isVisible || hasBeenVisible,
    hasBeenVisible,
  }
}

// Virtual scrolling component for large lists
interface VirtualScrollProps {
  items: any[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: any, index: number) => ReactNode
  overscan?: number
}

export function VirtualScroll({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
}: VirtualScrollProps) {
  const [scrollTop, setScrollTop] = useState(0)
  const scrollElementRef = useRef<HTMLDivElement>(null)

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  )

  const visibleItems = items.slice(startIndex, endIndex + 1)

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }

  return (
    <div
      ref={scrollElementRef}
      style={{ height: containerHeight, overflow: "auto" }}
      onScroll={handleScroll}
    >
      <div style={{ height: items.length * itemHeight, position: "relative" }}>
        <div
          style={{
            transform: `translateY(${startIndex * itemHeight}px)`,
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          {visibleItems.map((item, index) =>
            renderItem(item, startIndex + index)
          )}
        </div>
      </div>
    </div>
  )
}
