"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"

export type Language = "en" | "af" | "zu" | "xh" | "st"


export interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
  isRTL: boolean
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

const translations = {
  en: {
    // Navigation
    "nav.home": "Home",
    "nav.cart": "Cart",
    "nav.orders": "Orders",
    "nav.profile": "Profile",

    // Common
    "common.loading": "Loading...",
    "common.error": "Error",
    "common.success": "Success",
    "common.cancel": "Cancel",
    "common.save": "Save",
    "common.edit": "Edit",
    "common.delete": "Delete",
    "common.search": "Search",
    "common.filter": "Filter",
    "common.sort": "Sort",

    // App Name
    "app.name": "Spaza Smart Order",
    "app.tagline": "Your neighborhood grocery delivery",

    // Authentication
    "auth.login": "Sign In",
    "auth.signup": "Sign Up",
    "auth.logout": "Logout",
    "auth.email": "Email Address",
    "auth.password": "Password",
    "auth.confirmPassword": "Confirm Password",
    "auth.firstName": "First Name",
    "auth.lastName": "Last Name",
    "auth.phone": "Phone Number",
    "auth.address": "Address",
    "auth.welcomeBack": "Welcome Back",
    "auth.createAccount": "Create Account",
    "auth.forgotPassword": "Forgot password?",
    "auth.rememberMe": "Remember me",

    // Shopping
    "shop.addToCart": "Add to Cart",
    "shop.viewCart": "View Cart",
    "shop.checkout": "Checkout",
    "shop.total": "Total",
    "shop.subtotal": "Subtotal",
    "shop.delivery": "Delivery",
    "shop.specialOffers": "Special Offers",
    "shop.categories": "Categories",

    // Cart
    "cart.yourCart": "Your Cart",
    "cart.empty": "Your cart is empty",
    "cart.removeItem": "Remove Item",
    "cart.updateQuantity": "Update Quantity",
    "cart.proceedToCheckout": "Proceed to Checkout",

    // Orders
    "orders.myOrders": "My Orders",
    "orders.orderHistory": "Order History",
    "orders.trackOrder": "Track Order",
    "orders.reorder": "Reorder",
    "orders.orderStatus": "Order Status",

    // Profile
    "profile.myProfile": "My Profile",
    "profile.accountSettings": "Account Settings",
    "profile.personalInfo": "Personal Information",
    "profile.addresses": "Addresses",
    "profile.paymentMethods": "Payment Methods",

    // Language Settings
    "language.title": "Language Settings",
    "language.select": "Select Language",
    "language.current": "Current Language",
    "language.voiceOrdering": "Voice Ordering",
    "language.enableVoice": "Enable Voice Commands",
  },

  af: {
    // Navigation
    "nav.home": "Tuis",
    "nav.cart": "Mandjie",
    "nav.orders": "Bestellings",
    "nav.profile": "Profiel",

    // Common
    "common.loading": "Laai...",
    "common.error": "Fout",
    "common.success": "Sukses",
    "common.cancel": "Kanselleer",
    "common.save": "Stoor",
    "common.edit": "Wysig",
    "common.delete": "Verwyder",
    "common.search": "Soek",
    "common.filter": "Filter",
    "common.sort": "Sorteer",

    // App Name
    "app.name": "Spaza Smart Bestelling",
    "app.tagline": "Jou buurt kruideniersware aflewering",

    // Authentication
    "auth.login": "Teken In",
    "auth.signup": "Registreer",
    "auth.logout": "Teken Uit",
    "auth.email": "E-pos Adres",
    "auth.password": "Wagwoord",
    "auth.confirmPassword": "Bevestig Wagwoord",
    "auth.firstName": "Voornaam",
    "auth.lastName": "Van",
    "auth.phone": "Telefoonnommer",
    "auth.address": "Adres",
    "auth.welcomeBack": "Welkom Terug",
    "auth.createAccount": "Skep Rekening",
    "auth.forgotPassword": "Wagwoord vergeet?",
    "auth.rememberMe": "Onthou my",

    // Shopping
    "shop.addToCart": "Voeg by Mandjie",
    "shop.viewCart": "Bekyk Mandjie",
    "shop.checkout": "Afhandeling",
    "shop.total": "Totaal",
    "shop.subtotal": "Subtotaal",
    "shop.delivery": "Aflewering",
    "shop.specialOffers": "Spesiale Aanbiedinge",
    "shop.categories": "Kategorieë",

    // Cart
    "cart.yourCart": "Jou Mandjie",
    "cart.empty": "Jou mandjie is leeg",
    "cart.removeItem": "Verwyder Item",
    "cart.updateQuantity": "Wysig Hoeveelheid",
    "cart.proceedToCheckout": "Gaan voort na Afhandeling",

    // Orders
    "orders.myOrders": "My Bestellings",
    "orders.orderHistory": "Bestellingsgeskiedenis",
    "orders.trackOrder": "Volg Bestelling",
    "orders.reorder": "Herbestelling",
    "orders.orderStatus": "Bestellingstatus",

    // Profile
    "profile.myProfile": "My Profiel",
    "profile.accountSettings": "Rekeninginstellings",
    "profile.personalInfo": "Persoonlike Inligting",
    "profile.addresses": "Adresse",
    "profile.paymentMethods": "Betaalmetodes",

    // Language Settings
    "language.title": "Taalinstellings",
    "language.select": "Kies Taal",
    "language.current": "Huidige Taal",
    "language.voiceOrdering": "Stembestelling",
    "language.enableVoice": "Aktiveer Stembevele",
  },

  zu: {
    // Navigation
    "nav.home": "Ikhaya",
    "nav.cart": "Isitsha",
    "nav.orders": "Ama-oda",
    "nav.profile": "Iphrofayela",

    // Common
    "common.loading": "Iyalayisha...",
    "common.error": "Iphutha",
    "common.success": "Impumelelo",
    "common.cancel": "Khansela",
    "common.save": "Gcina",
    "common.edit": "Hlela",
    "common.delete": "Susa",
    "common.search": "Sesha",
    "common.filter": "Hlunga",
    "common.sort": "Hlela",

    // App Name
    "app.name": "Spaza Smart Order",
    "app.tagline": "Ukulethwa kokudla komakhelwane wakho",

    // Authentication
    "auth.login": "Ngena",
    "auth.signup": "Bhalisa",
    "auth.logout": "Phuma",
    "auth.email": "Ikheli le-imeyili",
    "auth.password": "Iphasiwedi",
    "auth.confirmPassword": "Qinisekisa Iphasiwedi",
    "auth.firstName": "Igama Lokuqala",
    "auth.lastName": "Isibongo",
    "auth.phone": "Inombolo Yocingo",
    "auth.address": "Ikheli",
    "auth.welcomeBack": "Sawubona Futhi",
    "auth.createAccount": "Dala I-akhawunti",
    "auth.forgotPassword": "Ukhohlwe iphasiwedi?",
    "auth.rememberMe": "Ngikhumbule",

    // Shopping
    "shop.addToCart": "Engeza Esitsheni",
    "shop.viewCart": "Buka Isitsha",
    "shop.checkout": "Khokha",
    "shop.total": "Isamba",
    "shop.subtotal": "Isamba Esincane",
    "shop.delivery": "Ukulethwa",
    "shop.specialOffers": "Amathuba Akhethekile",
    "shop.categories": "Izigaba",

    // Cart
    "cart.yourCart": "Isitsha Sakho",
    "cart.empty": "Isitsha sakho asinalutho",
    "cart.removeItem": "Susa Into",
    "cart.updateQuantity": "Shintsha Inani",
    "cart.proceedToCheckout": "Qhubeka Nokukhokha",

    // Orders
    "orders.myOrders": "Ama-oda Ami",
    "orders.orderHistory": "Umlando Wama-oda",
    "orders.trackOrder": "Landela I-oda",
    "orders.reorder": "Oda Futhi",
    "orders.orderStatus": "Isimo Se-oda",

    // Profile
    "profile.myProfile": "Iphrofayela Yami",
    "profile.accountSettings": "Izilungiselelo Ze-akhawunti",
    "profile.personalInfo": "Ulwazi Lomuntu Siqu",
    "profile.addresses": "Amakheli",
    "profile.paymentMethods": "Izindlela Zokukhokha",

    // Language Settings
    "language.title": "Izilungiselelo Zolimi",
    "language.select": "Khetha Ulimi",
    "language.current": "Ulimi Lwamanje",
    "language.voiceOrdering": "Ukuoda Ngezwi",
    "language.enableVoice": "Vumela Imiyalo Yezwi",
  },

  xh: {
    // Navigation
    "nav.home": "Ikhaya",
    "nav.cart": "Itroli",
    "nav.orders": "Ii-oda",
    "nav.profile": "Iprofayile",

    // Common
    "common.loading": "Iyalayisha...",
    "common.error": "Impazamo",
    "common.success": "Impumelelo",
    "common.cancel": "Rhoxisa",
    "common.save": "Gcina",
    "common.edit": "Hlela",
    "common.delete": "Cima",
    "common.search": "Khangela",
    "common.filter": "Hlula",
    "common.sort": "Hlela",

    // App Name
    "app.name": "Spaza Smart Order",
    "app.tagline": "Ukuhanjiswa kokutya kwindawo yakho",

    // Authentication
    "auth.login": "Ngena",
    "auth.signup": "Bhalisa",
    "auth.logout": "Phuma",
    "auth.email": "Idilesi ye-imeyile",
    "auth.password": "Iphaswedi",
    "auth.confirmPassword": "Qinisekisa Iphaswedi",
    "auth.firstName": "Igama Lokuqala",
    "auth.lastName": "Ifani",
    "auth.phone": "Inombolo Yomnxeba",
    "auth.address": "Idilesi",
    "auth.welcomeBack": "Wamkelekile Kwakhona",
    "auth.createAccount": "Yenza I-akhawunti",
    "auth.forgotPassword": "Ulibale iphaswedi?",
    "auth.rememberMe": "Ndikhumbule",

    // Shopping
    "shop.addToCart": "Yongeza Kwitroli",
    "shop.viewCart": "Jonga Itroli",
    "shop.checkout": "Hlawula",
    "shop.total": "Iyonke",
    "shop.subtotal": "Isamba Esincinci",
    "shop.delivery": "Ukuhanjiswa",
    "shop.specialOffers": "Iiofa Ezikhethekileyo",
    "shop.categories": "Iindidi",

    // Cart
    "cart.yourCart": "Itroli Yakho",
    "cart.empty": "Itroli yakho ayinanto",
    "cart.removeItem": "Susa Into",
    "cart.updateQuantity": "Tshintsha Ubungakanani",
    "cart.proceedToCheckout": "Qhubeka Nokuhlawula",

    // Orders
    "orders.myOrders": "Ii-oda Zam",
    "orders.orderHistory": "Imbali Yee-oda",
    "orders.trackOrder": "Landela I-oda",
    "orders.reorder": "Oda Kwakhona",
    "orders.orderStatus": "Imeko Ye-oda",

    // Profile
    "profile.myProfile": "Iprofayile Yam",
    "profile.accountSettings": "Iisetingi Ze-akhawunti",
    "profile.personalInfo": "Ulwazi Lomntu",
    "profile.addresses": "Iidilesi",
    "profile.paymentMethods": "Iindlela Zokuhlawula",

    // Language Settings
    "language.title": "Iisetingi Zolwimi",
    "language.select": "Khetha Ulwimi",
    "language.current": "Ulwimi Lwangoku",
    "language.voiceOrdering": "Ukuoda Ngelizwi",
    "language.enableVoice": "Vumela Imiyalelo Yelizwi",
  },

  st: {
    // Navigation
    "nav.home": "Hae",
    "nav.cart": "Sekoloto",
    "nav.orders": "Di-oda",
    "nav.profile": "Profaele",

    // Common
    "common.loading": "E a laisa...",
    "common.error": "Phoso",
    "common.success": "Katleho",
    "common.cancel": "Hlakola",
    "common.save": "Boloka",
    "common.edit": "Fetola",
    "common.delete": "Tlosa",
    "common.search": "Batla",
    "common.filter": "Kgetha",
    "common.sort": "Hlophisa",

    // App Name
    "app.name": "Spaza Smart Order",
    "app.tagline": "Ho romellwa ha dijo tsa heno haufi",

    // Authentication
    "auth.login": "Kena",
    "auth.signup": "Ngodisa",
    "auth.logout": "Tswa",
    "auth.email": "Aterese ya Imeile",
    "auth.password": "Phasewete",
    "auth.confirmPassword": "Netefatsa Phasewete",
    "auth.firstName": "Lebitso la Pele",
    "auth.lastName": "Sefane",
    "auth.phone": "Nomoro ya Mohala",
    "auth.address": "Aterese",
    "auth.welcomeBack": "O Amohelehile Hape",
    "auth.createAccount": "Theha Akhaonto",
    "auth.forgotPassword": "O lebetse phasewete?",
    "auth.rememberMe": "Nke hopole",

    // Shopping
    "shop.addToCart": "Eketsa Sekolotong",
    "shop.viewCart": "Sheba Sekoloto",
    "shop.checkout": "Lefa",
    "shop.total": "Kakaretso",
    "shop.subtotal": "Kakaretso e Nyane",
    "shop.delivery": "Ho romella",
    "shop.specialOffers": "Ditlhahiso tse Khethehileng",
    "shop.categories": "Mekhahlelo",

    // Cart
    "cart.yourCart": "Sekoloto sa Hao",
    "cart.empty": "Sekoloto sa hao ha se na letho",
    "cart.removeItem": "Tlosa Ntho",
    "cart.updateQuantity": "Fetola Palo",
    "cart.proceedToCheckout": "Tsoela pele ho Lefa",

    // Orders
    "orders.myOrders": "Di-oda tsa Ka",
    "orders.orderHistory": "Nalane ya Di-oda",
    "orders.trackOrder": "Latela Oda",
    "orders.reorder": "Oda Hape",
    "orders.orderStatus": "Boemo ba Oda",

    // Profile
    "profile.myProfile": "Profaele ya Ka",
    "profile.accountSettings": "Ditlhophiso tsa Akhaonto",
    "profile.personalInfo": "Tlhahisoleseding ya Motho",
    "profile.addresses": "Diaterese",
    "profile.paymentMethods": "Mekgwa ya ho Lefa",

    // Language Settings
    "language.title": "Ditlhophiso tsa Puo",
    "language.select": "Kgetha Puo",
    "language.current": "Puo ya Hajwale",
    "language.voiceOrdering": "Ho Oda ka Lentswe",
    "language.enableVoice": "Lumella Ditaelo tsa Lentswe",
  },
}

const languageNames = {
  en: "English",
  af: "Afrikaans",
  zu: "isiZulu",
  xh: "isiXhosa",
  st: "Sesotho",
}

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguageState] = useState<Language>("en")
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    const savedLanguage = localStorage.getItem("spaza-language") as Language
    if (savedLanguage && Object.keys(translations).includes(savedLanguage)) {
      setLanguageState(savedLanguage)
    }
  }, [])

  const setLanguage = (lang: Language) => {
    setLanguageState(lang)
    if (isClient) {
      localStorage.setItem("spaza-language", lang)
      document.documentElement.lang = lang
    }
  }

  type TranslationKey = keyof typeof translations["en"]

  const t = (key: TranslationKey): string => {
    return translations[language][key] || key
  }

  const isRTL = false // None of the South African languages are RTL

  return <LanguageContext.Provider value={{ language, setLanguage, t, isRTL }}>{children}</LanguageContext.Provider>
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}

export { languageNames }
