"use client"

import { createContext, useContext, useEffect, useState, ReactNode } from "react"
import { useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Id } from "@/convex/_generated/dataModel"
import { toast } from "sonner"
import { setUserId } from "@/lib/error-tracking"

interface User {
  _id: Id<"users">
  name: string
  email: string
  role: "customer" | "shop_owner" | "admin"
  phone?: string
  avatar?: string
  membershipTier?: string
  loyaltyPoints?: number
  isActive: boolean
  emailVerified: boolean
  lastLoginAt?: number
  createdAt: number
  updatedAt: number
}

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (email: string, password: string) => Promise<User>
  logout: () => Promise<void>
  register: (email: string, password: string, name: string, role?: "customer" | "shop_owner") => Promise<User>
  updateProfile: (updates: Partial<Pick<User, 'name' | 'phone' | 'avatar'>>) => Promise<void>
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  // Convex mutations
  const loginMutation = useMutation(api.users.login)
  const registerMutation = useMutation(api.users.register)
  const updateMutation = useMutation(api.users.update)
  const changePasswordMutation = useMutation(api.users.changePassword)

  // Check for existing session on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const currentUser = await getCurrentUser()
        if (currentUser) {
          setUser(currentUser)
          setUserId(currentUser._id)
        }
      } catch (error) {
        console.error("Auth check failed:", error)
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [])

  const getCurrentUser = async (): Promise<User | null> => {
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem('spaza_user')
      if (userData) {
        try {
          const parsedUser = JSON.parse(userData)
          // Validate that the user data has required fields
          if (parsedUser._id && parsedUser.email && parsedUser.name) {
            return parsedUser
          }
        } catch (error) {
          console.error("Error parsing user data:", error)
          localStorage.removeItem('spaza_user')
        }
      }
    }
    return null
  }

  const login = async (email: string, password: string): Promise<User> => {
    setLoading(true)
    try {
      // Password is hashed client-side before sending to the server
      const userData = await loginMutation({ email, password })
      const user = userData as User

      // Store user data in localStorage
      localStorage.setItem('spaza_user', JSON.stringify(user))
      setUser(user)
      setUserId(user._id)

      toast.success("Login successful!")
      return user
    } catch (error) {
      console.error("Login failed:", error)
      toast.error(error instanceof Error ? error.message : "Login failed")
      throw error
    } finally {
      setLoading(false)
    }
  }

  const register = async (email: string, password: string, name: string, role: User['role'] = 'customer'): Promise<User> => {
    setLoading(true)
    try {
      // Hash password before sending to server
      const hashedPassword = await hashPassword(password)
      const userId = await registerMutation({ 
        email, 
        password: hashedPassword, 
        name, 
        role 
      })
      const user = await login(email, password)

      toast.success("Registration successful!")
      return user
    } catch (error) {
      console.error("Registration failed:", error)
      toast.error(error instanceof Error ? error.message : "Registration failed")
      throw error
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      localStorage.removeItem('spaza_user')
      setUser(null)
      toast.success("Logged out successfully")
    } catch (error) {
      console.error("Logout failed:", error)
      toast.error("Logout failed")
      throw error
    }
  }

  const updateProfile = async (updates: Partial<Pick<User, 'name' | 'phone' | 'avatar'>>) => {
    if (!user) {
      throw new Error("No user logged in")
    }

    try {
      await updateMutation({ id: user._id, ...updates })

      // Update local user state
      const updatedUser = { ...user, ...updates }
      setUser(updatedUser)
      localStorage.setItem('spaza_user', JSON.stringify(updatedUser))

      toast.success("Profile updated successfully")
    } catch (error) {
      console.error("Profile update failed:", error)
      toast.error(error instanceof Error ? error.message : "Profile update failed")
      throw error
    }
  }

  const changePassword = async (currentPassword: string, newPassword: string): Promise<void> => {
    if (!user) throw new Error('Not authenticated')
    
    try {
      // Hash passwords before sending to server
      const currentHashedPassword = await hashPassword(currentPassword)
      const newHashedPassword = await hashPassword(newPassword)
      
      await changePasswordMutation({
        userId: user._id,
        currentPassword: currentHashedPassword,
        newPassword: newHashedPassword,
      })
      toast.success('Password updated successfully')
    } catch (error) {
      console.error('Failed to change password:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to change password')
      throw error
    }
  }
  
  const value = {
    user,
    loading,
    login,
    logout,
    register,
    updateProfile,
    changePassword,
  }
  
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
