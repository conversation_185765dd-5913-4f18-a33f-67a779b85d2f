# Technical Documentation - Spaza Smart Order

## Architecture Overview

The Spaza Smart Order application is built using a modern, real-time architecture that enables instant updates across all connected clients. The system is designed to handle the specific needs of spaza shop operations with real-time inventory management, order processing, and customer interactions.

### System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Customer      │    │   Shop Owner    │    │   Admin         │
│   Interface     │    │   Dashboard     │    │   Panel         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Next.js 14    │
                    │   Frontend      │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Convex        │
                    │   Backend       │
                    │   (Real-time)   │
                    └─────────────────┘
```

## Real-time Implementation

### Convex Real-time Subscriptions

The application leverages Convex's built-in real-time capabilities to provide instant updates across all connected clients. Here's how it works:

#### 1. Product Catalog Updates
```typescript
// Real-time product updates
const products = useQuery(api.products.list, { category })

// Automatically re-renders when:
// - New products are added
// - Product prices change
// - Stock levels update
// - Products are marked as out of stock
```

#### 2. Order Management
```typescript
// Real-time order tracking
const orders = useQuery(api.orders.list, { userId, status })

// Live updates for:
// - New order creation
// - Order status changes
// - Order modifications
// - Order cancellations
```

#### 3. Inventory Management
```typescript
// Real-time inventory tracking
const products = useQuery(api.products.list, { category: "all" })

// Instant updates for:
// - Stock level changes
// - Low stock alerts
// - Out of stock notifications
// - Product availability
```

### Real-time Components

#### ConvexDashboard Component
```typescript
// components/admin/convex-dashboard.tsx
export function ConvexDashboard({ shopOwnerId }: ConvexDashboardProps) {
  const allOrders = useQuery(api.orders.list, { userId: "all" })
  const products = useQuery(api.products.list, { category: "all" })
  
  // Real-time statistics calculation
  const totalOrders = allOrders?.length || 0
  const pendingOrders = allOrders?.filter(order => order.status === 'pending').length || 0
  // ... other real-time calculations
}
```

#### ConvexNotifications Component
```typescript
// components/convex-notifications.tsx
export function ConvexNotifications({ userId, isShopOwner }: ConvexNotificationsProps) {
  const orders = useQuery(api.orders.list, { userId })
  
  // Real-time notification generation
  const notifications = orders?.map(order => ({
    id: order._id,
    message: `Order ${order.orderNumber} is ${order.status}`,
    type: order.status,
    read: false,
    createdAt: new Date(order.createdAt).toISOString(),
  })) || []
}
```

## Database Schema Design

### Optimized for Real-time Operations

The database schema is designed to support efficient real-time queries and updates:

#### Products Table
```typescript
// convex/schema.ts
products: defineTable({
  name: v.string(),
  price: v.number(),
  originalPrice: v.optional(v.number()),
  image: v.string(),
  category: v.string(),
  rating: v.optional(v.number()),
  reviews: v.optional(v.number()),
  inStock: v.boolean(),
  discount: v.optional(v.number()),
  isSpecialOffer: v.optional(v.boolean()),
  stockQuantity: v.optional(v.number()),
}).index("by_category", ["category"])
```

**Key Features:**
- `stockQuantity` field for precise inventory tracking
- `inStock` boolean for quick availability checks
- Category index for efficient filtering
- Optional fields for flexible product data

#### Orders Table
```typescript
orders: defineTable({
  userId: v.string(),
  orderNumber: v.string(),
  status: v.union(v.literal("pending"), v.literal("processing"), v.literal("delivered"), v.literal("cancelled")),
  items: v.array(v.object({
    productId: v.id("products"),
    name: v.string(),
    price: v.number(),
    quantity: v.number(),
    image: v.string(),
  })),
  subtotal: v.number(),
  deliveryFee: v.number(),
  total: v.number(),
  deliveryAddress: v.string(),
  deliveryOption: v.string(),
  estimatedDelivery: v.string(),
  createdAt: v.number(),
  updatedAt: v.number(),
})
  .index("by_user", ["userId"])
  .index("by_status", ["status"])
  .index("by_created_at", ["createdAt"])
```

**Key Features:**
- Multiple indexes for efficient querying
- `updatedAt` field for change tracking
- Embedded items array for order details
- Status enum for type safety

## Real-time Hooks Implementation

### Custom Hooks for Real-time Data

#### useConvexProducts Hook
```typescript
// hooks/use-convex-products.ts
export function useConvexProducts(category?: string) {
  const products = useQuery(api.products.list, { category })
  
  return {
    products: products || [],
    loading: products === undefined,
    error: null
  }
}
```

#### useConvexOrders Hook
```typescript
// hooks/use-convex-orders.ts
export function useConvexOrders(userId: string, status?: string) {
  const orders = useQuery(api.orders.list, { userId, status })
  
  return {
    orders: orders || [],
    loading: orders === undefined,
    error: null
  }
}
```

#### useConvexCart Hook
```typescript
// hooks/use-convex-cart.ts
export function useConvexCart(userId: string) {
  const cartItems = useQuery(api.cart.list, { userId })
  
  return {
    cartItems: cartItems || [],
    loading: cartItems === undefined,
    error: null
  }
}
```

## Backend Functions (Convex)

### Product Operations
```typescript
// convex/products.ts

// List products with optional category filtering
export const list = query({
  args: { category: v.optional(v.string()) },
  handler: async (ctx, args) => {
    if (args.category && args.category !== "all") {
      return await ctx.db
        .query("products")
        .withIndex("by_category", (q) => q.eq("category", args.category as string))
        .collect()
    }
    return await ctx.db.query("products").collect()
  },
})

// Update stock levels with real-time updates
export const updateStock = mutation({
  args: { 
    id: v.id("products"),
    stockQuantity: v.number(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.id, { 
      stockQuantity: args.stockQuantity,
      inStock: args.stockQuantity > 0
    })
  },
})
```

### Order Operations
```typescript
// convex/orders.ts

// List orders with real-time updates
export const list = query({
  args: {
    userId: v.string(),
    status: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Support for shop owner view (all orders)
    if (args.userId === "all") {
      const query = ctx.db.query("orders").withIndex("by_created_at")
      
      if (args.status && args.status !== "all") {
        const orders = await query.collect()
        return orders.filter((order) => order.status === args.status)
      }
      
      return await query.order("desc").collect()
    }

    // Regular user query
    const query = ctx.db.query("orders").withIndex("by_user", (q) => q.eq("userId", args.userId))
    // ... rest of implementation
  },
})

// Update order status with real-time notifications
export const updateStatus = mutation({
  args: {
    id: v.id("orders"),
    status: v.union(v.literal("pending"), v.literal("processing"), v.literal("delivered"), v.literal("cancelled")),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.id, { 
      status: args.status,
      updatedAt: Date.now()
    })
  },
})
```

## Performance Optimizations

### 1. Efficient Indexing
- Category-based product filtering
- User-based order queries
- Status-based order filtering
- Time-based ordering

### 2. Real-time Subscription Management
- Automatic subscription cleanup
- Efficient re-rendering with React
- Minimal data transfer with Convex

### 3. Component Optimization
- Conditional rendering based on loading states
- Memoization of expensive calculations
- Efficient state management

## Error Handling

### Frontend Error Handling
```typescript
// Example error handling in components
const { products, loading, error } = useConvexProducts(category)

if (loading) {
  return <LoadingSpinner />
}

if (error) {
  return <ErrorMessage message={error} />
}

return <ProductList products={products} />
```

### Backend Error Handling
```typescript
// Example error handling in Convex functions
export const updateStock = mutation({
  args: { 
    id: v.id("products"),
    stockQuantity: v.number(),
  },
  handler: async (ctx, args) => {
    try {
      const product = await ctx.db.get(args.id)
      if (!product) {
        throw new Error("Product not found")
      }
      
      await ctx.db.patch(args.id, { 
        stockQuantity: args.stockQuantity,
        inStock: args.stockQuantity > 0
      })
    } catch (error) {
      console.error("Error updating stock:", error)
      throw error
    }
  },
})
```

## Security Considerations

### 1. Data Validation
- Input validation with Convex validators
- Type safety with TypeScript
- Sanitization of user inputs

### 2. Access Control
- User-based data filtering
- Shop owner permissions
- Admin-only operations

### 3. Real-time Security
- Secure WebSocket connections
- Authentication tokens (planned)
- Rate limiting (planned)

## Monitoring and Debugging

### 1. Convex Dashboard
- Real-time function monitoring
- Database query performance
- Error tracking and logging

### 2. Frontend Monitoring
- React DevTools for component debugging
- Network tab for API calls
- Console logging for development

### 3. Performance Monitoring
- Real-time subscription metrics
- Component render performance
- Database query optimization

## Deployment Considerations

### 1. Environment Configuration
```env
# Development
NEXT_PUBLIC_CONVEX_URL=https://your-dev-convex-url.convex.cloud

# Production
NEXT_PUBLIC_CONVEX_URL=https://your-prod-convex-url.convex.cloud
```

### 2. Convex Deployment
```bash
# Deploy to production
npx convex deploy --prod

# Deploy to development
npx convex dev
```

### 3. Next.js Deployment
- Vercel (recommended for seamless integration)
- Environment variables configuration
- Automatic deployments from Git

## Future Enhancements

### 1. Authentication Integration
- Convex Auth implementation
- User session management
- Role-based access control

### 2. Advanced Real-time Features
- Live chat support
- Real-time delivery tracking
- Push notifications

### 3. Performance Improvements
- Caching strategies
- Database optimization
- CDN integration

### 4. Monitoring and Analytics
- Real-time analytics dashboard
- Performance monitoring
- Error tracking and alerting

---

This technical documentation provides a comprehensive overview of the real-time implementation in the Spaza Smart Order application. The system is designed to be scalable, maintainable, and efficient while providing the real-time capabilities essential for modern spaza shop operations.
