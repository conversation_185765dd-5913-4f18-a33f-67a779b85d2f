"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { ArrowLeft, CreditCard, Smartphone, Building2, Wallet, Clock, Shield, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { api } from "@/convex/_generated/api"
import { useQuery } from "convex/react"

export default function PaymentHub() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("methods")
  const user = useQuery(api.users.get, { id: "demo-user" as any })



  const paymentMethods = [
    {
      id: "mtn-mobile-money",
      name: "MTN Mobile Money",
      type: "mobile",
      icon: "📱",
      status: "active",
      description: "Pay with your MTN Mobile Money wallet",
      fees: "No fees for payments under R500",
    },
    {
      id: "vodacom-mpesa",
      name: "Vodacom M-Pesa",
      type: "mobile",
      icon: "📱",
      status: "active",
      description: "Pay with your Vodacom M-Pesa wallet",
      fees: "R2.50 transaction fee",
    },
    {
      id: "standard-bank",
      name: "Standard Bank EFT",
      type: "bank",
      icon: "🏦",
      status: "active",
      description: "Direct bank transfer from Standard Bank",
      fees: "Free for online banking users",
    },
    {
      id: "fnb",
      name: "FNB Pay",
      type: "bank",
      icon: "🏦",
      status: "active",
      description: "Pay with FNB banking app",
      fees: "Free instant transfers",
    },
    {
      id: "absa",
      name: "ABSA CashSend",
      type: "bank",
      icon: "🏦",
      status: "active",
      description: "ABSA mobile banking payment",
      fees: "R5.00 transaction fee",
    },
    {
      id: "snapscan",
      name: "SnapScan",
      type: "ewallet",
      icon: "📲",
      status: "active",
      description: "Scan QR code to pay with SnapScan",
      fees: "No additional fees",
    },
    {
      id: "zapper",
      name: "Zapper",
      type: "ewallet",
      icon: "📲",
      status: "active",
      description: "Pay with Zapper mobile app",
      fees: "No additional fees",
    },
    {
      id: "payfast",
      name: "PayFast",
      type: "card",
      icon: "💳",
      status: "active",
      description: "Credit/Debit cards via PayFast",
      fees: "2.9% + R2.00 per transaction",
    },
  ]

  const creditOptions = [
    {
      id: "payflex",
      name: "PayFlex",
      description: "Buy now, pay in 4 interest-free installments",
      icon: "⏰",
      terms: "25% today, 25% every 2 weeks",
      eligibility: "Minimum R100 purchase",
    },
    {
      id: "mobicred",
      name: "Mobicred",
      description: "Revolving credit facility",
      icon: "💳",
      terms: "Up to R50,000 credit limit",
      eligibility: "Credit check required",
    },
    {
      id: "layaway",
      name: "Lay-by",
      description: "Reserve items, pay over time",
      icon: "🏪",
      terms: "20% deposit, 3 months to pay",
      eligibility: "Available for orders over R200",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4 flex items-center gap-3">
        <Button variant="ghost" size="sm" className="text-white hover:bg-blue-700 p-2" onClick={() => router.back()}>
          <ArrowLeft className="w-5 h-5" />
        </Button>
        <h1 className="text-lg font-semibold">Payment Methods</h1>
      </div>

      <div className="p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="methods">Payment Methods</TabsTrigger>
            <TabsTrigger value="credit">Credit Options</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="methods" className="space-y-4">
            {/* Mobile Money */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Smartphone className="w-5 h-5 text-blue-600" />
                  Mobile Money
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {paymentMethods
                  .filter((method) => method.type === "mobile")
                  .map((method) => (
                    <div key={method.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{method.icon}</span>
                        <div>
                          <p className="font-medium">{method.name}</p>
                          <p className="text-sm text-gray-600">{method.description}</p>
                          <p className="text-xs text-green-600">{method.fees}</p>
                        </div>
                      </div>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Active
                      </Badge>
                    </div>
                  ))}
              </CardContent>
            </Card>

            {/* Bank Transfers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="w-5 h-5 text-blue-600" />
                  Bank Transfers
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {paymentMethods
                  .filter((method) => method.type === "bank")
                  .map((method) => (
                    <div key={method.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{method.icon}</span>
                        <div>
                          <p className="font-medium">{method.name}</p>
                          <p className="text-sm text-gray-600">{method.description}</p>
                          <p className="text-xs text-green-600">{method.fees}</p>
                        </div>
                      </div>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Active
                      </Badge>
                    </div>
                  ))}
              </CardContent>
            </Card>

            {/* E-Wallets */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wallet className="w-5 h-5 text-blue-600" />
                  E-Wallets & QR Payments
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {paymentMethods
                  .filter((method) => method.type === "ewallet")
                  .map((method) => (
                    <div key={method.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{method.icon}</span>
                        <div>
                          <p className="font-medium">{method.name}</p>
                          <p className="text-sm text-gray-600">{method.description}</p>
                          <p className="text-xs text-green-600">{method.fees}</p>
                        </div>
                      </div>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Active
                      </Badge>
                    </div>
                  ))}
              </CardContent>
            </Card>

            {/* Cards */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="w-5 h-5 text-blue-600" />
                  Credit & Debit Cards
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {paymentMethods
                  .filter((method) => method.type === "card")
                  .map((method) => (
                    <div key={method.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{method.icon}</span>
                        <div>
                          <p className="font-medium">{method.name}</p>
                          <p className="text-sm text-gray-600">{method.description}</p>
                          <p className="text-xs text-orange-600">{method.fees}</p>
                        </div>
                      </div>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Active
                      </Badge>
                    </div>
                  ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="credit" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5 text-blue-600" />
                  Buy Now, Pay Later Options
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {creditOptions.map((option) => (
                  <div key={option.id} className="p-4 border rounded-lg">
                    <div className="flex items-start gap-3">
                      <span className="text-2xl">{option.icon}</span>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">{option.name}</h3>
                        <p className="text-sm text-gray-600 mb-2">{option.description}</p>
                        <div className="space-y-1">
                          <p className="text-xs text-blue-600">
                            <strong>Terms:</strong> {option.terms}
                          </p>
                          <p className="text-xs text-gray-500">
                            <strong>Eligibility:</strong> {option.eligibility}
                          </p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        Apply
                      </Button>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Credit Score Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5 text-green-600" />
                  Your Credit Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                  <div>
                    <p className="font-medium text-green-800">Good Credit Standing</p>
                    <p className="text-sm text-green-600">You're eligible for all credit options</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Payment Preferences</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Auto-select preferred payment</p>
                    <p className="text-sm text-gray-600">Use your default payment method for faster checkout</p>
                  </div>
                  <Button variant="outline" size="sm">
                    Configure
                  </Button>
                </div>

                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Payment notifications</p>
                    <p className="text-sm text-gray-600">Get SMS/email alerts for payment confirmations</p>
                  </div>
                  <Button variant="outline" size="sm">
                    Manage
                  </Button>
                </div>

                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Security settings</p>
                    <p className="text-sm text-gray-600">Two-factor authentication and payment limits</p>
                  </div>
                  <Button variant="outline" size="sm">
                    Setup
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Security Notice */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5 text-blue-600" />
                  Security & Privacy
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                    <p>All payments are encrypted with 256-bit SSL security</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                    <p>We never store your full card details on our servers</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                    <p>PCI DSS compliant payment processing</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                    <p>24/7 fraud monitoring and protection</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
