"use client"

import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Id } from "@/convex/_generated/dataModel"
import { useState, useEffect } from "react"
import { toast } from "sonner"

export function useConvexProducts(category?: string) {
  const [error, setError] = useState<string | null>(null)
  const products = useQuery(api.products.list, { category })

  useEffect(() => {
    // Reset error when query changes
    setError(null)
  }, [category])

  // Handle query errors
  useEffect(() => {
    if (products === undefined) {
      // Still loading, no error yet
      return
    }

    // If products is null, there might be an error
    if (products === null) {
      const errorMessage = "Failed to load products"
      setError(errorMessage)
      toast.error(errorMessage)
    }
  }, [products])

  return {
    products: products || [],
    loading: products === undefined,
    error,
    retry: () => {
      setError(null)
      // The query will automatically retry
    }
  }
}

export function useConvexProduct(id: Id<"products">) {
  const [error, setError] = useState<string | null>(null)
  const product = useQuery(api.products.get, { id })

  useEffect(() => {
    setError(null)
  }, [id])

  useEffect(() => {
    if (product === undefined) {
      return
    }

    if (product === null) {
      const errorMessage = "Product not found"
      setError(errorMessage)
      toast.error(errorMessage)
    }
  }, [product])

  return {
    product,
    loading: product === undefined,
    error,
    retry: () => {
      setError(null)
    }
  }
}


