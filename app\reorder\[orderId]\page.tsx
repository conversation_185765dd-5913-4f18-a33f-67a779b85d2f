"use client"

import { useState, useEffect } from "react"
import { useRouter, usePara<PERSON> } from "next/navigation"
import { ArrowLeft, Plus, Minus, ShoppingCart } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface OrderItem {
  id: string
  name: string
  quantity: number
  price: number
  image: string
  inStock: boolean
}

interface Order {
  id: string
  orderNumber: string
  date: string
  items: OrderItem[]
  total: number
}

export default function ReorderPage() {
  const router = useRouter()
  const params = useParams()
  const orderId = params.orderId as string

  const [order, setOrder] = useState<Order | null>(null)
  const [reorderItems, setReorderItems] = useState<OrderItem[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Mock order data - in real app, this would come from API/database
  const mockOrders: Order[] = [
    {
      id: "1",
      orderNumber: "ORD-2024-001",
      date: "2024-01-15",
      items: [
        {
          id: "1",
          name: "White Star Maize Meal 5kg",
          quantity: 2,
          price: 65.0,
          image: "/placeholder-1l6vi.png",
          inStock: true,
        },
        {
          id: "2",
          name: "Coca-Cola Can 300ml",
          quantity: 3,
          price: 8.5,
          image: "/refreshing-cola-can.png",
          inStock: true,
        },
      ],
      total: 205.5,
    },
    {
      id: "2",
      orderNumber: "ORD-2024-002",
      date: "2024-01-12",
      items: [
        {
          id: "3",
          name: "Fresh Milk 1L",
          quantity: 2,
          price: 18.99,
          image: "/placeholder-b1ual.png",
          inStock: false,
        },
        {
          id: "4",
          name: "Bread Loaf White",
          quantity: 1,
          price: 12.5,
          image: "/white-bread-loaf.png",
          inStock: true,
        },
      ],
      total: 100.48,
    },
  ]

  useEffect(() => {
    // Simulate API call
    const foundOrder = mockOrders.find((o) => o.id === orderId)
    if (foundOrder) {
      setOrder(foundOrder)
      setReorderItems([...foundOrder.items])
    }
    setIsLoading(false)
  }, [orderId])

  const updateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity < 0) return
    setReorderItems((items) => items.map((item) => (item.id === itemId ? { ...item, quantity: newQuantity } : item)))
  }

  const removeItem = (itemId: string) => {
    setReorderItems((items) => items.filter((item) => item.id !== itemId))
  }

  const calculateTotal = () => {
    return reorderItems.reduce((total, item) => total + item.price * item.quantity, 0)
  }

  const addToCart = () => {
    const itemsToAdd = reorderItems.filter((item) => item.quantity > 0)
    if (itemsToAdd.length === 0) return

    // In real app, this would add items to cart via API/context
    console.log("Adding items to cart:", itemsToAdd)

    // Show success message and redirect
    alert(`${itemsToAdd.length} items added to cart!`)
    router.push("/")
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading order...</p>
        </div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Order not found</h2>
          <p className="text-gray-600 mb-4">The order you're looking for doesn't exist.</p>
          <Button onClick={() => router.push("/orders")} className="bg-blue-600 hover:bg-blue-700">
            Back to Orders
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="flex items-center gap-3">
          <Button variant="ghost" size="sm" className="text-white hover:bg-blue-700 p-2" onClick={() => router.back()}>
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="text-lg font-semibold">Reorder Items</h1>
            <p className="text-blue-100 text-sm">From {order.orderNumber}</p>
          </div>
        </div>
      </div>

      {/* Order Info */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-600">Original Order Date</p>
            <p className="font-medium">{new Date(order.date).toLocaleDateString()}</p>
          </div>
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            {order.items.length} items
          </Badge>
        </div>
      </div>

      {/* Items List */}
      <div className="p-4 space-y-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <p className="text-sm text-blue-800">
            <strong>Note:</strong> Adjust quantities as needed. Items marked as out of stock will be excluded.
          </p>
        </div>

        {reorderItems.map((item) => (
          <div key={item.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-start gap-3">
              <img
                src={item.image || "/placeholder.svg"}
                alt={item.name}
                className="w-16 h-16 object-cover rounded-md"
              />
              <div className="flex-1">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="font-medium text-gray-900">{item.name}</h3>
                    <p className="text-sm text-gray-600">R {item.price.toFixed(2)} each</p>
                    {!item.inStock && (
                      <Badge variant="destructive" className="mt-1 text-xs">
                        Out of Stock
                      </Badge>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    onClick={() => removeItem(item.id)}
                  >
                    Remove
                  </Button>
                </div>

                {item.inStock && (
                  <div className="flex items-center justify-between mt-3">
                    <div className="flex items-center gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-8 h-8 p-0 bg-transparent"
                        onClick={() => updateQuantity(item.id, item.quantity - 1)}
                        disabled={item.quantity <= 0}
                      >
                        <Minus className="w-4 h-4" />
                      </Button>
                      <span className="font-medium text-lg min-w-[2rem] text-center">{item.quantity}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-8 h-8 p-0 bg-transparent"
                        onClick={() => updateQuantity(item.id, item.quantity + 1)}
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-blue-600">R {(item.price * item.quantity).toFixed(2)}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Order Summary */}
      <div className="bg-white border-t border-gray-200 p-4 space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-lg font-medium text-gray-900">Total</span>
          <span className="text-xl font-semibold text-blue-600">R {calculateTotal().toFixed(2)}</span>
        </div>

        <Button
          className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3"
          onClick={addToCart}
          disabled={reorderItems.filter((item) => item.quantity > 0 && item.inStock).length === 0}
        >
          <ShoppingCart className="w-5 h-5 mr-2" />
          Add {reorderItems.filter((item) => item.quantity > 0 && item.inStock).length} Items to Cart
        </Button>

        <Button variant="outline" className="w-full bg-transparent" onClick={() => router.push("/orders")}>
          Back to Orders
        </Button>
      </div>

      {/* Bottom padding */}
      <div className="h-6"></div>
    </div>
  )
}
