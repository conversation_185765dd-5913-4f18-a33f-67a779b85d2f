import { query, mutation } from "./_generated/server"
import { v } from "convex/values"

export const list = query({
  args: {
    userId: v.string(),
    status: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // If userId is "all", return all orders (for shop owners)
    if (args.userId === "all") {
      const query = ctx.db.query("orders").withIndex("by_created_at")
      
      if (args.status && args.status !== "all") {
        const orders = await query.collect()
        return orders.filter((order) => order.status === args.status)
      }
      
      return await query.order("desc").collect()
    }

    // Regular user query
    const query = ctx.db.query("orders").withIndex("by_user", (q) => q.eq("userId", args.userId))

    if (args.status && args.status !== "all") {
      const orders = await query.collect()
      return orders.filter((order) => order.status === args.status)
    }

    return await query.order("desc").collect()
  },
})

export const create = mutation({
  args: {
    userId: v.string(),
    items: v.array(
      v.object({
        productId: v.id("products"),
        name: v.string(),
        price: v.number(),
        quantity: v.number(),
        image: v.string(),
      }),
    ),
    subtotal: v.number(),
    deliveryFee: v.number(),
    total: v.number(),
    deliveryAddress: v.string(),
    deliveryOption: v.string(),
  },
  handler: async (ctx, args) => {
    const orderNumber = `ORD${Date.now()}`
    const estimatedDelivery = args.deliveryOption === "express" ? "Next business day" : "2-3 business days"
    const now = Date.now()

    return await ctx.db.insert("orders", {
      ...args,
      orderNumber,
      status: "pending",
      estimatedDelivery,
      createdAt: now,
      updatedAt: now,
    })
  },
})

export const updateStatus = mutation({
  args: {
    id: v.id("orders"),
    status: v.union(v.literal("pending"), v.literal("processing"), v.literal("delivered"), v.literal("cancelled")),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.id, { 
      status: args.status,
      updatedAt: Date.now()
    })
  },
})
