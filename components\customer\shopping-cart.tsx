"use client"

import { Id } from "@/convex/_generated/dataModel"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "sonner"
import { Minus, Plus, Trash2, ShoppingBag, CreditCard } from "lucide-react"
import { useConvexCart } from "@/hooks/use-convex-cart"
import { LoadingCard, LoadingSpinner } from "@/components/ui/loading"
import { ErrorDisplay, EmptyState } from "@/components/ui/error-display"

type Product = {
  _id: Id<"products">
  name: string
  price: number
  image: string
  category: string
  inStock: boolean
  stockQuantity?: number
  originalPrice?: number
  isSpecialOffer?: boolean
  _creationTime: number
}

type CartItem = {
  _id: Id<"cart">
  userId: string
  productId: Id<"products">
  quantity: number
  selected: boolean
  product: Product | null
  _creationTime: number
}

interface ShoppingCartProps {
  userId: string
  onCheckout?: () => void
}

export function ShoppingCart({ userId, onCheckout }: ShoppingCartProps) {
  const {
    cartItems,
    selectedItems,
    totalPrice,
    loading,
    error,
    updateQuantity,
    toggleSelect,
    removeItem,
    clearCart: clearCartAction
  } = useConvexCart(userId || '')

  const handleQuantityChange = async (cartItemId: Id<"cart">, newQuantity: number) => {
    if (newQuantity < 1) return
    
    const cartItem = cartItems.find(item => item._id === cartItemId)
    if (!cartItem?.product) return
    
    const maxQuantity = cartItem.product.stockQuantity || 1
    if (newQuantity > maxQuantity) {
      toast.error(`Only ${maxQuantity} items available in stock`)
      return
    }
    
    try {
      await updateQuantity(cartItemId, newQuantity)
      toast.success("Quantity updated")
    } catch (error) {
      console.error("Failed to update quantity:", error)
      toast.error("Failed to update quantity. Please try again.")
    }
  }

  const handleToggleSelect = async (cartItemId: Id<"cart">, selected: boolean) => {
    try {
      await toggleSelect(cartItemId, selected)
    } catch (error) {
      console.error("Failed to update selection:", error)
      toast.error("Failed to update selection. Please try again.")
    }
  }

  const handleRemoveItem = async (cartItemId: Id<"cart">, productName: string) => {
    if (confirm(`Are you sure you want to remove ${productName} from your cart?`)) {
      try {
        await removeItem(cartItemId)
        toast.success("Item removed from cart")
      } catch (error) {
        console.error("Failed to remove item:", error)
        toast.error("Failed to remove item. Please try again.")
      }
    }
  }

  const handleClearCart = async () => {
    if (confirm("Are you sure you want to clear your cart?")) {
      try {
        await clearCartAction()
        toast.success("Cart cleared")
      } catch (error) {
        console.error("Failed to clear cart:", error)
        toast.error("Failed to clear cart. Please try again.")
      }
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="h-8 bg-gray-200 rounded w-32 animate-pulse"></div>
          <div className="h-8 bg-gray-200 rounded w-24 animate-pulse"></div>
        </div>
        <LoadingCard />
        <LoadingCard />
        <LoadingCard />
      </div>
    )
  }

  if (error) {
    return (
      <ErrorDisplay
        title="Failed to load cart"
        message={error.message || 'Failed to load cart items. Please try again.'}
        onRetry={() => window.location.reload()}
      />
    )
  }

  if (cartItems.length === 0) {
    return (
      <EmptyState
        title="Your cart is empty"
        message="Looks like you haven't added any items yet"
        icon={<ShoppingBag className="w-16 h-16" />}
        action={
          <Button
            onClick={() => window.location.href = '/home'}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Continue Shopping
          </Button>
        }
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Cart Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Shopping Cart</h2>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handleClearCart}>
            Clear Cart
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Cart Items */}
        <div className="lg:col-span-2 space-y-4">
          {cartItems.length === 0 ? (
            <div className="text-center py-12">
              <ShoppingBag className="h-12 w-12 mx-auto text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">Your cart is empty</h3>
              <p className="mt-1 text-gray-500">Start adding some products to your cart.</p>
            </div>
          ) : (
            cartItems.map((item: CartItem) => {
              // Skip rendering if product is null
              if (!item.product) return null;
              
              return (
                <Card key={item._id} className="overflow-hidden">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      {/* Selection Checkbox */}
                      <input
                        type="checkbox"
                        checked={item.selected}
                        onChange={(e) => handleToggleSelect(item._id, e.target.checked)}
                        className="rounded border-gray-300 h-5 w-5"
                        aria-label={`Select ${item.product.name}`}
                      />

                      {/* Product Image */}
                      <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                        <img
                          src={item.product.image || "/placeholder.svg"}
                          alt={item.product.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = "/placeholder.svg";
                          }}
                        />
                      </div>

                      {/* Product Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <h3 className="font-semibold text-sm">{item.product.name}</h3>
                        </div>
                        
                        {!item.product.inStock && (
                          <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded">
                            Out of Stock
                          </span>
                        )}
                        {item.product.isSpecialOffer && (
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded ml-2">
                            Special Offer
                          </span>
                        )}
                        <div className="flex items-center gap-2 mt-1">
                          <p className="font-semibold text-lg">
                            R{item.product.price.toFixed(2)}
                          </p>
                        </div>
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => handleQuantityChange(item._id, item.quantity - 1)}
                          disabled={item.quantity <= 1}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <span className="w-8 text-center">{item.quantity}</span>
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => handleQuantityChange(item._id, item.quantity + 1)}
                          disabled={!item.product?.inStock || 
                            (item.product.stockQuantity !== undefined && 
                             item.quantity >= item.product.stockQuantity)}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>

                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>

        {/* Order Summary */}
        <div className="lg:col-span-1">
          <Card className="sticky top-4">
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>R{totalPrice.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Delivery</span>
                  <span>R{selectedItems.length > 0 ? '15.00' : '0.00'}</span>
                </div>
                <div className="border-t pt-2 mt-2">
                  <div className="flex justify-between font-bold">
                    <span>Total</span>
                    <span>R{(totalPrice + (selectedItems.length > 0 ? 15 : 0)).toFixed(2)}</span>
                  </div>
                </div>
              </div>

              <Button 
                className="w-full" 
                size="lg"
                onClick={onCheckout}
                disabled={selectedItems.length === 0 || loading}
              >
                <CreditCard className="mr-2 h-4 w-4" />
                Proceed to Checkout
              </Button>

              {selectedItems.length === 0 && (
                <p className="text-sm text-gray-500 text-center">
                  Select items to checkout
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
