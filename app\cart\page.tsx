"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { ShoppingCart } from "@/components/customer/shopping-cart"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "sonner"

export default function CartPage() {
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()
  const [cartCount, setCartCount] = useState(0)

  useEffect(() => {
    if (!authLoading && !user) {
      router.push("/login?redirect=/cart")
      toast.info("Please sign in to view your cart")
    }
  }, [user, authLoading, router])

  const handleCheckout = () => {
    if (cartCount === 0) {
      toast.warning("Your cart is empty")
      return
    }
    router.push("/checkout")
  }

  const handleCartUpdate = (count: number) => {
    setCartCount(count)
  }

  if (authLoading || !user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4 flex items-center gap-3 sticky top-0 z-10">
        <Button 
          variant="ghost" 
          size="sm" 
          className="text-white hover:bg-blue-700 p-2" 
          onClick={() => router.back()}
        >
          <ArrowLeft className="w-5 h-5" />
        </Button>
        <h1 className="text-lg font-semibold">Shopping Cart</h1>
        {cartCount > 0 && (
          <div className="ml-auto bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
            {cartCount} item{cartCount !== 1 ? 's' : ''}
          </div>
        )}
      </div>

      {/* Real-time Shopping Cart */}
      <div className="p-4 max-w-6xl mx-auto">
        <ShoppingCart
          userId={user._id}
          onCheckout={handleCheckout}
        />
      </div>
    </div>
  )
}



