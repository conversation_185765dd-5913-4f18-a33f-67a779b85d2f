import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { GeistSans } from "geist/font/sans"
import { GeistMono } from "geist/font/mono"
import "./globals.css"
import { ConvexClientProvider } from "@/components/convex-client-provider"
import { ThemeProvider } from "next-themes"
import { LanguageProvider } from "@/contexts/language-context"
import { AuthProvider } from "@/contexts/auth-context"
import { ClientOnly } from "@/components/client-only"
import ErrorBoundary from "@/components/error-boundary"
import { SecurityProvider } from "@/components/security/security-provider"
import { InstallPrompt } from "@/components/pwa/install-prompt"
import { Toaster } from "@/components/ui/sonner"

export const metadata: Metadata = {
  title: "Spaza Smart Order",
  description: "Your neighborhood grocery delivery app",
  generator: "v0.app",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="antialiased light" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
        <meta name="theme-color" content="#2563eb" />
        
        {/* PWA Support */}
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Spaza Smart Order" />
        <meta name="application-name" content="Spaza Smart Order" />
        
        {/* Microsoft Application */}
        <meta name="msapplication-TileColor" content="#2563eb" />
        <meta name="msapplication-config" content="/browserconfig.xml" />

        <link rel="manifest" href="/manifest.json" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <link rel="mask-icon" href="/icons/safari-pinned-tab.svg" color="#2563eb" />

        <style>{`
html {
  font-family: ${GeistSans.style.fontFamily};
  --font-sans: ${GeistSans.variable};
  --font-mono: ${GeistMono.variable};
}
        `}</style>
      </head>
      <body>
        <ErrorBoundary>
          <ConvexClientProvider>
            <ClientOnly>
              <ThemeProvider
                attribute="class"
                defaultTheme="light"
                enableSystem={false}
                disableTransitionOnChange
              >
                <LanguageProvider>
                  <SecurityProvider>
                    <AuthProvider>
                      {children}
                      <Toaster />
                      <InstallPrompt />
                    </AuthProvider>
                  </SecurityProvider>
                </LanguageProvider>
              </ThemeProvider>
            </ClientOnly>
          </ConvexClientProvider>
        </ErrorBoundary>
      </body>
    </html>
  )
}
