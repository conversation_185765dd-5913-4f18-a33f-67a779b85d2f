import { defineSchema, defineTable } from "convex/server"
import { v } from "convex/values"

export default defineSchema({
  users: defineTable({
    email: v.string(),
    name: v.string(),
    phone: v.optional(v.string()),
    avatar: v.optional(v.string()),
    membershipTier: v.optional(v.string()),
    loyaltyPoints: v.optional(v.number()),
    role: v.union(v.literal("customer"), v.literal("shop_owner"), v.literal("admin")),
    passwordHash: v.string(),
    isActive: v.boolean(),
    emailVerified: v.boolean(),
    lastLoginAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_email", ["email"])
    .index("by_role", ["role"])
    .index("by_active", ["isActive"]),

  products: defineTable({
    name: v.string(),
    price: v.number(),
    originalPrice: v.optional(v.number()),
    image: v.string(),
    category: v.string(),
    rating: v.optional(v.number()),
    reviews: v.optional(v.number()),
    inStock: v.boolean(),
    discount: v.optional(v.number()),
    isSpecialOffer: v.optional(v.boolean()),
    stockQuantity: v.optional(v.number()),
  }).index("by_category", ["category"]),

  cart: defineTable({
    userId: v.string(),
    productId: v.id("products"),
    quantity: v.number(),
    selected: v.boolean(),
  }).index("by_user", ["userId"]),

  orders: defineTable({
    userId: v.string(),
    orderNumber: v.string(),
    status: v.union(
      v.literal("pending"),
      v.literal("confirmed"),
      v.literal("processing"),
      v.literal("preparing"),
      v.literal("ready"),
      v.literal("out_for_delivery"),
      v.literal("delivered"),
      v.literal("cancelled")
    ),
    paymentStatus: v.union(
      v.literal("pending"),
      v.literal("processing"),
      v.literal("completed"),
      v.literal("failed"),
      v.literal("refunded")
    ),
    paymentMethod: v.union(
      v.literal("card"),
      v.literal("cash_on_delivery"),
      v.literal("eft"),
      v.literal("mobile_money"),
      v.literal("store_credit")
    ),
    paymentReference: v.optional(v.string()),
    paymentId: v.optional(v.string()),
    items: v.array(
      v.object({
        productId: v.id("products"),
        name: v.string(),
        price: v.number(),
        quantity: v.number(),
        image: v.string(),
      }),
    ),
    subtotal: v.number(),
    deliveryFee: v.number(),
    paymentFees: v.number(),
    total: v.number(),
    deliveryAddress: v.string(),
    deliveryOption: v.string(),
    estimatedDelivery: v.string(),
    notes: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_status", ["status"])
    .index("by_payment_status", ["paymentStatus"])
    .index("by_payment_method", ["paymentMethod"])
    .index("by_created_at", ["createdAt"]),
})
