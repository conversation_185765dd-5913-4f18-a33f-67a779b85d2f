import { query, mutation } from "./_generated/server"
import { v } from "convex/values"

// Note: Password hashing should be done on the client side before calling these mutations
// This is because Convex doesn't support async operations like bcrypt in mutations

// Client-side password hashing function (to be used in the frontend)
// This is a placeholder - the actual implementation should be in the frontend code
type PasswordHash = string

// Simple verification function that just compares the hashes
function verifyPasswordHash(passwordHash: string, storedHash: string): boolean {
  return passwordHash === storedHash
}

export const get = query({
  args: { id: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.id)
    if (!user) return null

    // Don't return password hash
    const { passwordHash, ...userWithoutPassword } = user
    return userWithoutPassword
  },
})

export const getByEmail = query({
  args: { email: v.string() },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first()

    if (!user) return null

    // Don't return password hash
    const { passwordHash, ...userWithoutPassword } = user
    return userWithoutPassword
  },
})

export const register = mutation({
  args: {
    email: v.string(),
    name: v.string(),
    password: v.string(),
    phone: v.optional(v.string()),
    role: v.optional(v.union(v.literal("customer"), v.literal("shop_owner"))),
  },
  handler: async (ctx, args) => {
    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first()

    if (existingUser) {
      throw new Error("User with this email already exists")
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(args.email)) {
      throw new Error("Invalid email format")
    }

    // Password should be hashed client-side before calling this mutation
    const now = Date.now()
    const userId = await ctx.db.insert("users", {
      email: args.email,
      name: args.name,
      phone: args.phone,
      passwordHash: args.password, // Expecting pre-hashed password from client
      role: args.role || "customer",
      membershipTier: "Bronze",
      loyaltyPoints: 0,
      isActive: true,
      emailVerified: false,
      createdAt: now,
      updatedAt: now,
    })

    return userId
  },
})

export const login = mutation({
  args: {
    email: v.string(),
    password: v.string(),
  },
  handler: async (ctx, args) => {
    // Find user by email
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first()

    if (!user) {
      throw new Error("Invalid email or password")
    }

    // Check if user is active
    if (!user.isActive) {
      throw new Error("Account is deactivated")
    }

    // Verify password hash (password should be hashed client-side)
    if (!verifyPasswordHash(args.password, user.passwordHash)) {
      throw new Error("Invalid email or password")
    }

    // Update last login time
    await ctx.db.patch(user._id, {
      lastLoginAt: Date.now(),
      updatedAt: Date.now(),
    })

    // Return user without password hash
    const { passwordHash, ...userWithoutPassword } = user
    return userWithoutPassword
  },
})

export const update = mutation({
  args: {
    id: v.id("users"),
    name: v.optional(v.string()),
    phone: v.optional(v.string()),
    avatar: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args

    const user = await ctx.db.get(id)
    if (!user) {
      throw new Error("User not found")
    }

    await ctx.db.patch(id, {
      ...updates,
      updatedAt: Date.now(),
    })
  },
})

export const changePassword = mutation({
  args: {
    userId: v.id("users"),
    currentPassword: v.string(),
    newPassword: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId)
    if (!user) {
      throw new Error("User not found")
    }

    // Verify current password
    const isCurrentPasswordValid = verifyPasswordHash(args.currentPassword, user.passwordHash)
    if (!isCurrentPasswordValid) {
      throw new Error("Current password is incorrect")
    }

    // Validate new password
    if (args.newPassword.length < 6) {
      throw new Error("New password must be at least 6 characters long")
    }

    // Update password (newPassword should be hashed client-side)
    await ctx.db.patch(args.userId, {
      passwordHash: args.newPassword,
      updatedAt: Date.now(),
    })
  },
})

export const deactivateUser = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      isActive: false,
      updatedAt: Date.now(),
    })
  },
})

export const listUsers = query({
  args: {
    role: v.optional(v.union(v.literal("customer"), v.literal("shop_owner"), v.literal("admin"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let users

    if (args.role) {
      users = await ctx.db
        .query("users")
        .withIndex("by_role", (q) => q.eq("role", args.role!))
        .order("desc")
        .take(args.limit || 50)
    } else {
      users = await ctx.db
        .query("users")
        .order("desc")
        .take(args.limit || 50)
    }

    // Remove password hashes from all users
    return users.map(user => {
      const { passwordHash, ...userWithoutPassword } = user
      return userWithoutPassword
    })
  },
})
