# Spaza Smart Order - Real-time Grocery Delivery App

A modern, real-time grocery delivery application built with Next.js 14, Convex, and TailwindCSS, designed specifically for spaza shop owners and customers in South Africa.

## 🚀 Features

### For Customers
- **Real-time Product Catalog**: Browse products with live updates
- **Smart Shopping Cart**: Real-time cart synchronization across devices
- **Order Tracking**: Live order status updates
- **Multiple Payment Options**: Secure payment processing
- **Delivery Options**: Express and standard delivery
- **Loyalty Program**: Points-based rewards system
- **Multi-language Support**: English and local languages

### For Shop Owners
- **Real-time Dashboard**: Live statistics and analytics
- **Order Management**: Real-time order processing and status updates
- **Inventory Management**: Live stock tracking with low-stock alerts
- **Product Management**: Add, edit, and manage products
- **Analytics**: Sales reports and performance metrics
- **Notifications**: Real-time alerts for new orders and low stock

## 🛠 Tech Stack

- **Frontend**: Next.js 14, React 19, TypeScript
- **Backend**: Convex (Real-time database and API)
- **Styling**: TailwindCSS, Radix UI components
- **State Management**: Convex real-time subscriptions
- **Authentication**: Convex Auth (planned)
- **Deployment**: Vercel (recommended)

## 📁 Project Structure

```
spaza-cart/
├── app/                          # Next.js 14 App Router
│   ├── admin/                    # Admin dashboard pages
│   │   ├── dashboard/           # Main dashboard
│   │   ├── inventory/           # Inventory management
│   │   ├── products/            # Product management
│   │   └── analytics/           # Analytics and reports
│   ├── checkout/                # Checkout process
│   ├── home/                    # Customer home page
│   ├── orders/                  # Order management
│   └── profile/                 # User profile
├── components/                   # Reusable components
│   ├── ui/                      # Base UI components
│   ├── admin/                   # Admin-specific components
│   ├── convex-notifications.tsx # Real-time notifications
│   └── convex-client-provider.tsx # Convex provider
├── convex/                      # Convex backend
│   ├── schema.ts               # Database schema
│   ├── products.ts             # Product operations
│   ├── orders.ts               # Order operations
│   ├── cart.ts                 # Cart operations
│   └── users.ts                # User operations
├── hooks/                       # Custom React hooks
│   ├── use-convex-products.ts  # Product hooks
│   ├── use-convex-orders.ts    # Order hooks
│   └── use-convex-cart.ts      # Cart hooks
└── lib/                        # Utility functions
    └── utils.ts                # Common utilities
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm or pnpm
- Convex account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd spaza-cart
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Set up Convex**
   ```bash
   npx convex dev
   ```
   Follow the prompts to create a new Convex project.

4. **Environment Variables**
   Create a `.env.local` file:
   ```env
   NEXT_PUBLIC_CONVEX_URL=your_convex_url_here
   ```

5. **Run the development server**
   ```bash
   pnpm dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📊 Database Schema

### Products Table
```typescript
{
  _id: Id<"products">
  name: string
  price: number
  originalPrice?: number
  image: string
  category: string
  rating?: number
  reviews?: number
  inStock: boolean
  discount?: number
  isSpecialOffer?: boolean
  stockQuantity?: number
}
```

### Orders Table
```typescript
{
  _id: Id<"orders">
  userId: string
  orderNumber: string
  status: "pending" | "processing" | "delivered" | "cancelled"
  items: Array<{
    productId: Id<"products">
    name: string
    price: number
    quantity: number
    image: string
  }>
  subtotal: number
  deliveryFee: number
  total: number
  deliveryAddress: string
  deliveryOption: string
  estimatedDelivery: string
  createdAt: number
  updatedAt: number
}
```

### Cart Table
```typescript
{
  _id: Id<"cart">
  userId: string
  productId: Id<"products">
  quantity: number
  selected: boolean
}
```

## 🔄 Real-time Features

### Live Updates
- **Products**: Real-time product catalog updates
- **Orders**: Live order status changes
- **Inventory**: Real-time stock level updates
- **Cart**: Synchronized cart across devices
- **Notifications**: Instant alerts for shop owners

### Convex Subscriptions
The app uses Convex's built-in real-time subscriptions:

```typescript
// Example: Real-time products
const products = useQuery(api.products.list, { category })

// Example: Real-time orders
const orders = useQuery(api.orders.list, { userId, status })
```

## 🎨 UI Components

### Design System
- **Colors**: Modern, accessible color palette
- **Typography**: Clean, readable fonts
- **Spacing**: Consistent spacing system
- **Components**: Reusable Radix UI components

### Key Components
- `ConvexDashboard`: Real-time admin dashboard
- `ConvexNotifications`: Live notification system
- `RealTimeInventory`: Live inventory management
- `RealTimeOrders`: Live order management

## 📱 Pages & Routes

### Customer Routes
- `/` - Home page with product catalog
- `/home` - Customer dashboard
- `/checkout` - Checkout process
- `/orders` - Order history
- `/profile` - User profile

### Admin Routes
- `/admin/dashboard` - Main dashboard
- `/admin/inventory` - Inventory management
- `/admin/products/add` - Add new product
- `/admin/products/edit/[id]` - Edit product
- `/admin/analytics` - Analytics and reports

## 🔧 Development

### Adding New Features

1. **Database Changes**: Update `convex/schema.ts`
2. **Backend Logic**: Add functions to appropriate files in `convex/`
3. **Frontend Components**: Create components in `components/`
4. **Hooks**: Add custom hooks in `hooks/`
5. **Pages**: Add new pages in `app/`

### Code Style
- Use TypeScript for type safety
- Follow React best practices
- Use TailwindCSS for styling
- Implement proper error handling
- Add loading states for better UX

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Environment Variables for Production
```env
NEXT_PUBLIC_CONVEX_URL=your_production_convex_url
NEXT_PUBLIC_CONVEX_DEPLOYMENT=your_production_deployment
```

## 📈 Performance

### Optimizations
- **Server Components**: Use React Server Components where possible
- **Image Optimization**: Next.js automatic image optimization
- **Code Splitting**: Automatic code splitting with Next.js
- **Real-time Efficiency**: Convex's optimized real-time subscriptions

### Monitoring
- Use Convex dashboard for backend monitoring
- Vercel Analytics for frontend performance
- Real-time error tracking with Convex

## 🔒 Security

### Data Protection
- Row Level Security (RLS) with Convex
- Input validation and sanitization
- Secure authentication (planned)
- HTTPS enforcement

### Best Practices
- Never expose sensitive data to client
- Validate all user inputs
- Use proper error handling
- Regular security updates

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation
- Open an issue on GitHub
- Contact the development team

## 🔮 Future Enhancements

### Planned Features
- **Authentication**: User login/signup
- **Payment Integration**: Stripe/PayPal integration
- **Mobile App**: React Native mobile app
- **Advanced Analytics**: Detailed reporting
- **Multi-vendor Support**: Multiple shop support
- **Delivery Tracking**: GPS-based delivery tracking
- **Push Notifications**: Mobile push notifications
- **Offline Support**: PWA capabilities

### Technical Improvements
- **Caching**: Redis caching layer
- **CDN**: Content delivery network
- **Monitoring**: Advanced monitoring and alerting
- **Testing**: Comprehensive test suite
- **CI/CD**: Automated testing and deployment

---

Built with ❤️ for South African spaza shops
