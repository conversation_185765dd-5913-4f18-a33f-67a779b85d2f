// Payment utilities and types for Spaza Smart Order

export type PaymentMethod = 
  | "card"
  | "cash_on_delivery" 
  | "eft"
  | "mobile_money"
  | "store_credit"

export type PaymentStatus = 
  | "pending"
  | "processing" 
  | "completed"
  | "failed"
  | "cancelled"
  | "refunded"

export interface PaymentDetails {
  method: PaymentMethod
  amount: number
  currency: string
  reference?: string
  metadata?: Record<string, any>
}

export interface PaymentResult {
  success: boolean
  paymentId?: string
  reference?: string
  message?: string
  error?: string
}

// South African payment methods configuration
export const PAYMENT_METHODS = {
  card: {
    id: "card",
    name: "Credit/Debit Card",
    description: "Visa, Mastercard, American Express",
    icon: "💳",
    enabled: true,
    fees: 0.029, // 2.9% + R2.90
    fixedFee: 2.90,
  },
  cash_on_delivery: {
    id: "cash_on_delivery",
    name: "Cash on Delivery",
    description: "Pay with cash when your order arrives",
    icon: "💵",
    enabled: true,
    fees: 0,
    fixedFee: 0,
  },
  eft: {
    id: "eft",
    name: "EFT/Bank Transfer",
    description: "Electronic Funds Transfer",
    icon: "🏦",
    enabled: true,
    fees: 0,
    fixedFee: 5.00, // R5 flat fee
  },
  mobile_money: {
    id: "mobile_money",
    name: "Mobile Money",
    description: "MTN Mobile Money, Vodacom M-Pesa",
    icon: "📱",
    enabled: true,
    fees: 0.015, // 1.5%
    fixedFee: 1.00,
  },
  store_credit: {
    id: "store_credit",
    name: "Store Credit",
    description: "Use your store credit balance",
    icon: "🎁",
    enabled: true,
    fees: 0,
    fixedFee: 0,
  },
} as const

// Calculate payment fees
export function calculatePaymentFees(amount: number, method: PaymentMethod): number {
  const config = PAYMENT_METHODS[method]
  if (!config) return 0
  
  const percentageFee = amount * config.fees
  return percentageFee + config.fixedFee
}

// Format currency for South African Rand
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2,
  }).format(amount)
}

// Validate payment method availability
export function isPaymentMethodAvailable(method: PaymentMethod): boolean {
  return PAYMENT_METHODS[method]?.enabled ?? false
}

// Generate payment reference
export function generatePaymentReference(): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substring(2, 8)
  return `SPZ-${timestamp}-${random}`.toUpperCase()
}

// Mock payment processing functions (replace with real implementations)
export async function processCardPayment(details: PaymentDetails): Promise<PaymentResult> {
  // In production, integrate with Stripe, PayFast, or other SA payment processors
  await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate processing
  
  // Mock success/failure
  const success = Math.random() > 0.1 // 90% success rate
  
  if (success) {
    return {
      success: true,
      paymentId: `card_${Date.now()}`,
      reference: generatePaymentReference(),
      message: "Payment processed successfully"
    }
  } else {
    return {
      success: false,
      error: "Card payment failed. Please check your card details and try again."
    }
  }
}

export async function processMobileMoneyPayment(details: PaymentDetails): Promise<PaymentResult> {
  // In production, integrate with MTN Mobile Money, Vodacom M-Pesa APIs
  await new Promise(resolve => setTimeout(resolve, 3000)) // Simulate processing
  
  const success = Math.random() > 0.05 // 95% success rate
  
  if (success) {
    return {
      success: true,
      paymentId: `mobile_${Date.now()}`,
      reference: generatePaymentReference(),
      message: "Mobile money payment processed successfully"
    }
  } else {
    return {
      success: false,
      error: "Mobile money payment failed. Please check your mobile money balance and try again."
    }
  }
}

export async function processEFTPayment(details: PaymentDetails): Promise<PaymentResult> {
  // In production, integrate with bank APIs or payment gateways
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  return {
    success: true,
    paymentId: `eft_${Date.now()}`,
    reference: generatePaymentReference(),
    message: "EFT payment initiated. Please complete the transfer using the provided reference."
  }
}

export async function processCashOnDeliveryPayment(details: PaymentDetails): Promise<PaymentResult> {
  // Cash on delivery doesn't require immediate processing
  return {
    success: true,
    paymentId: `cod_${Date.now()}`,
    reference: generatePaymentReference(),
    message: "Cash on delivery order confirmed. Please have exact change ready."
  }
}

export async function processStoreCreditPayment(details: PaymentDetails): Promise<PaymentResult> {
  // In production, check user's store credit balance
  await new Promise(resolve => setTimeout(resolve, 500))
  
  // Mock store credit check
  const hasEnoughCredit = Math.random() > 0.2 // 80% have enough credit
  
  if (hasEnoughCredit) {
    return {
      success: true,
      paymentId: `credit_${Date.now()}`,
      reference: generatePaymentReference(),
      message: "Store credit payment processed successfully"
    }
  } else {
    return {
      success: false,
      error: "Insufficient store credit balance. Please choose another payment method."
    }
  }
}

// Main payment processor
export async function processPayment(details: PaymentDetails): Promise<PaymentResult> {
  try {
    switch (details.method) {
      case "card":
        return await processCardPayment(details)
      case "mobile_money":
        return await processMobileMoneyPayment(details)
      case "eft":
        return await processEFTPayment(details)
      case "cash_on_delivery":
        return await processCashOnDeliveryPayment(details)
      case "store_credit":
        return await processStoreCreditPayment(details)
      default:
        return {
          success: false,
          error: "Unsupported payment method"
        }
    }
  } catch (error) {
    console.error("Payment processing error:", error)
    return {
      success: false,
      error: "Payment processing failed. Please try again."
    }
  }
}

// Payment validation
export function validatePaymentAmount(amount: number): boolean {
  return amount > 0 && amount <= 50000 // Max R50,000 per transaction
}

export function validatePaymentMethod(method: string): method is PaymentMethod {
  return Object.keys(PAYMENT_METHODS).includes(method)
}
