"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, Truck, MapPin, Users, AlertTriangle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function AdminDeliveryManagement() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("overview")

  const deliveryStats = {
    activeDeliveries: 24,
    completedToday: 156,
    averageDeliveryTime: 42,
    onTimeDeliveryRate: 94.2,
  }

  const activeDrivers = [
    {
      id: "driver-1",
      name: "<PERSON>",
      zone: "Southern Suburbs",
      status: "delivering",
      currentDeliveries: 2,
      completedToday: 8,
      rating: 4.8,
      location: "Soweto",
    },
    {
      id: "driver-2",
      name: "<PERSON>",
      zone: "Northern Suburbs",
      status: "available",
      currentDeliveries: 0,
      completedToday: 12,
      rating: 4.9,
      location: "Sandton",
    },
    {
      id: "driver-3",
      name: "David Mokoena",
      zone: "City Center",
      status: "offline",
      currentDeliveries: 0,
      completedToday: 6,
      rating: 4.7,
      location: "CBD",
    },
  ]

  const pendingDeliveries = [
    {
      id: "del-001",
      orderNumber: "ORD-2024-001",
      customer: "Alice Mthembu",
      zone: "Southern Suburbs",
      priority: "high",
      timeWaiting: "15 mins",
      items: 3,
      value: 245.5,
    },
    {
      id: "del-002",
      orderNumber: "ORD-2024-002",
      customer: "Bob Ndaba",
      zone: "Northern Suburbs",
      priority: "normal",
      timeWaiting: "8 mins",
      items: 5,
      value: 189.75,
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4 flex items-center gap-3">
        <Button variant="ghost" size="sm" className="text-white hover:bg-blue-700 p-2" onClick={() => router.back()}>
          <ArrowLeft className="w-5 h-5" />
        </Button>
        <h1 className="text-lg font-semibold">Delivery Management</h1>
      </div>

      <div className="p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="drivers">Drivers</TabsTrigger>
            <TabsTrigger value="zones">Zones</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Stats Cards */}
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">{deliveryStats.activeDeliveries}</div>
                  <div className="text-sm text-gray-600">Active Deliveries</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">{deliveryStats.completedToday}</div>
                  <div className="text-sm text-gray-600">Completed Today</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-yellow-600">{deliveryStats.averageDeliveryTime}m</div>
                  <div className="text-sm text-gray-600">Avg Delivery Time</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-purple-600">{deliveryStats.onTimeDeliveryRate}%</div>
                  <div className="text-sm text-gray-600">On-Time Rate</div>
                </CardContent>
              </Card>
            </div>

            {/* Pending Deliveries */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5 text-orange-600" />
                  Pending Assignments ({pendingDeliveries.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {pendingDeliveries.map((delivery) => (
                  <div key={delivery.id} className="p-3 border rounded-lg">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h4 className="font-semibold text-gray-900">{delivery.orderNumber}</h4>
                        <p className="text-sm text-gray-600">{delivery.customer}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        {delivery.priority === "high" && (
                          <Badge variant="destructive" className="bg-red-100 text-red-800">
                            High Priority
                          </Badge>
                        )}
                        <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                          Waiting {delivery.timeWaiting}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>{delivery.zone}</span>
                      <span>
                        {delivery.items} items • R {delivery.value}
                      </span>
                    </div>
                    <Button size="sm" className="w-full mt-2 bg-blue-600 hover:bg-blue-700">
                      Assign Driver
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Real-time Map */}
            <Card>
              <CardHeader>
                <CardTitle>Live Delivery Map</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Real-time delivery tracking map</p>
                    <p className="text-sm text-gray-400">Driver locations and delivery routes</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="drivers" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-blue-600" />
                  Driver Status ({activeDrivers.length} drivers)
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {activeDrivers.map((driver) => (
                  <div key={driver.id} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-semibold text-gray-900">{driver.name}</h3>
                        <p className="text-sm text-gray-600">{driver.zone}</p>
                        <p className="text-xs text-gray-500">Currently in {driver.location}</p>
                      </div>
                      <Badge
                        variant="secondary"
                        className={
                          driver.status === "delivering"
                            ? "bg-blue-100 text-blue-800"
                            : driver.status === "available"
                              ? "bg-green-100 text-green-800"
                              : "bg-gray-100 text-gray-800"
                        }
                      >
                        {driver.status}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Active Deliveries</p>
                        <p className="font-semibold">{driver.currentDeliveries}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Completed Today</p>
                        <p className="font-semibold">{driver.completedToday}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Rating</p>
                        <p className="font-semibold">{driver.rating} ⭐</p>
                      </div>
                    </div>
                    <div className="flex gap-2 mt-3">
                      <Button size="sm" variant="outline" className="flex-1 bg-transparent">
                        <MapPin className="w-4 h-4 mr-2" />
                        Track
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1 bg-transparent">
                        <Truck className="w-4 h-4 mr-2" />
                        Assign Order
                      </Button>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="zones" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Zone Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold">Southern Suburbs</h4>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Optimal
                      </Badge>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Avg Delivery Time</p>
                        <p className="font-semibold">38 mins</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Active Drivers</p>
                        <p className="font-semibold">6 drivers</p>
                      </div>
                      <div>
                        <p className="text-gray-500">On-Time Rate</p>
                        <p className="font-semibold">96%</p>
                      </div>
                    </div>
                  </div>

                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold">Northern Suburbs</h4>
                      <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                        Busy
                      </Badge>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Avg Delivery Time</p>
                        <p className="font-semibold">52 mins</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Active Drivers</p>
                        <p className="font-semibold">8 drivers</p>
                      </div>
                      <div>
                        <p className="text-gray-500">On-Time Rate</p>
                        <p className="font-semibold">89%</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
