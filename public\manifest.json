{"name": "Spaza Smart Order", "short_name": "Spaza Order", "description": "Smart ordering system for South African spaza shops - connecting communities with local businesses", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#2563eb", "orientation": "portrait-primary", "scope": "/", "lang": "en-ZA", "dir": "ltr", "categories": ["shopping", "food", "business"], "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "screenshots": [{"src": "/screenshots/mobile-home.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "Home screen showing product categories"}, {"src": "/screenshots/mobile-products.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "Product catalog with search and filters"}, {"src": "/screenshots/mobile-cart.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "Shopping cart and checkout"}, {"src": "/screenshots/desktop-home.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "Desktop home screen"}], "shortcuts": [{"name": "Browse Products", "short_name": "Products", "description": "Browse available products", "url": "/products", "icons": [{"src": "/icons/shortcut-products.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "View Cart", "short_name": "<PERSON><PERSON>", "description": "View shopping cart", "url": "/cart", "icons": [{"src": "/icons/shortcut-cart.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "My Orders", "short_name": "Orders", "description": "View order history", "url": "/orders", "icons": [{"src": "/icons/shortcut-orders.png", "sizes": "96x96", "type": "image/png"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "protocol_handlers": [{"protocol": "web+spaza", "url": "/handle-protocol?url=%s"}]}