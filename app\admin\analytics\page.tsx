"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  ArrowLeft,
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Calendar,
  Download,
  Filter,
  BarChart3,
  PieChart,
  Target,
  Clock,
} from "lucide-react"

export default function BusinessAnalytics() {
  const router = useRouter()
  const [selectedPeriod, setSelectedPeriod] = useState("7d")
  const [selectedMetric, setSelectedMetric] = useState("revenue")

  const periods = [
    { value: "1d", label: "Today" },
    { value: "7d", label: "7 Days" },
    { value: "30d", label: "30 Days" },
    { value: "90d", label: "90 Days" },
  ]

  const keyMetrics = [
    {
      title: "Total Revenue",
      value: "R 12,450",
      change: "+15.3%",
      trend: "up",
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-50",
      description: "vs last period",
    },
    {
      title: "Orders",
      value: "156",
      change: "+8.2%",
      trend: "up",
      icon: ShoppingCart,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      description: "total orders",
    },
    {
      title: "Customers",
      value: "89",
      change: "+12.1%",
      trend: "up",
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      description: "unique customers",
    },
    {
      title: "Avg Order Value",
      value: "R 79.81",
      change: "-2.4%",
      trend: "down",
      icon: Target,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      description: "per order",
    },
  ]

  const topProducts = [
    {
      name: "Coca-Cola Can 300ml",
      sku: "CC-300ML-001",
      sales: 45,
      revenue: "R 382.50",
      profit: "R 112.50",
      margin: "29.4%",
      trend: "up",
    },
    {
      name: "White Star Maize Meal 5kg",
      sku: "WSM-5KG-001",
      sales: 12,
      revenue: "R 780.00",
      profit: "R 240.00",
      margin: "30.8%",
      trend: "up",
    },
    {
      name: "Lay's Potato Chips 120g",
      sku: "LAY-120G-001",
      sales: 28,
      revenue: "R 447.72",
      profit: "R 125.72",
      margin: "28.1%",
      trend: "down",
    },
    {
      name: "Fresh Chicken Breast 1kg",
      sku: "FCB-1KG-001",
      sales: 8,
      revenue: "R 719.92",
      profit: "R 199.92",
      margin: "27.8%",
      trend: "up",
    },
  ]

  const salesData = [
    { day: "Mon", sales: 1200, orders: 15 },
    { day: "Tue", sales: 1800, orders: 22 },
    { day: "Wed", sales: 1600, orders: 19 },
    { day: "Thu", sales: 2200, orders: 28 },
    { day: "Fri", sales: 2800, orders: 35 },
    { day: "Sat", sales: 3200, orders: 42 },
    { day: "Sun", sales: 2400, orders: 31 },
  ]

  const categoryPerformance = [
    { category: "Beverages", sales: "R 3,240", percentage: 26, color: "bg-blue-500" },
    { category: "Staples", sales: "R 2,890", percentage: 23, color: "bg-green-500" },
    { category: "Snacks", sales: "R 2,156", percentage: 17, color: "bg-purple-500" },
    { category: "Personal Care", sales: "R 1,834", percentage: 15, color: "bg-orange-500" },
    { category: "Cleaning", sales: "R 1,245", percentage: 10, color: "bg-red-500" },
    { category: "Others", sales: "R 1,085", percentage: 9, color: "bg-gray-500" },
  ]

  const customerInsights = [
    {
      title: "Peak Hours",
      value: "2PM - 6PM",
      description: "Highest sales activity",
      icon: Clock,
    },
    {
      title: "Repeat Customers",
      value: "67%",
      description: "Customer retention rate",
      icon: Users,
    },
    {
      title: "Avg Items/Order",
      value: "3.2",
      description: "Items per transaction",
      icon: Package,
    },
    {
      title: "Top Day",
      value: "Saturday",
      description: "Best performing day",
      icon: Calendar,
    },
  ]

  const maxSales = Math.max(...salesData.map((d) => d.sales))

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button variant="ghost" size="sm" onClick={() => router.back()}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Business Analytics</h1>
                <p className="text-sm text-gray-600">Track your store performance and insights</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {periods.map((period) => (
                  <option key={period.value} value={period.value}>
                    {period.label}
                  </option>
                ))}
              </select>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-1" />
                Filter
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="p-4 space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {keyMetrics.map((metric, index) => (
            <Card key={index} className="border-0 shadow-sm">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className={`p-2 rounded-lg ${metric.bgColor}`}>
                    <metric.icon className={`h-5 w-5 ${metric.color}`} />
                  </div>
                  <div className="flex items-center space-x-1">
                    {metric.trend === "up" ? (
                      <TrendingUp className="h-4 w-4 text-green-600" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-red-600" />
                    )}
                    <span
                      className={`text-sm font-medium ${metric.trend === "up" ? "text-green-600" : "text-red-600"}`}
                    >
                      {metric.change}
                    </span>
                  </div>
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
                  <p className="text-sm text-gray-600">{metric.description}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Sales Chart */}
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Sales Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">Sales (R)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">Orders</span>
                  </div>
                </div>
                <p className="text-sm text-gray-500">Last 7 days</p>
              </div>

              {/* Simple Bar Chart */}
              <div className="space-y-3">
                {salesData.map((data, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="w-12 text-sm text-gray-600 font-medium">{data.day}</div>
                    <div className="flex-1 flex items-center space-x-2">
                      <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
                        <div
                          className="bg-blue-500 h-6 rounded-full flex items-center justify-end pr-2"
                          style={{ width: `${(data.sales / maxSales) * 100}%` }}
                        >
                          <span className="text-xs text-white font-medium">R {data.sales}</span>
                        </div>
                      </div>
                      <div className="w-16 text-sm text-gray-600 text-right">{data.orders} orders</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Top Products */}
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <Package className="h-5 w-5 mr-2" />
              Top Performing Products
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topProducts.map((product, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full text-blue-600 font-bold text-sm">
                      {index + 1}
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 text-sm">{product.name}</h3>
                      <p className="text-xs text-gray-500">{product.sku}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-4">
                      <div className="text-center">
                        <p className="text-sm font-medium text-gray-900">{product.sales}</p>
                        <p className="text-xs text-gray-500">sold</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-medium text-gray-900">{product.revenue}</p>
                        <p className="text-xs text-gray-500">revenue</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-medium text-green-600">{product.profit}</p>
                        <p className="text-xs text-gray-500">{product.margin}</p>
                      </div>
                      <div className="w-6">
                        {product.trend === "up" ? (
                          <TrendingUp className="h-4 w-4 text-green-600" />
                        ) : (
                          <TrendingDown className="h-4 w-4 text-red-600" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Category Performance & Customer Insights */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Category Performance */}
          <Card className="border-0 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <PieChart className="h-5 w-5 mr-2" />
                Category Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {categoryPerformance.map((category, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-4 h-4 rounded-full ${category.color}`}></div>
                      <span className="text-sm font-medium text-gray-900">{category.category}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className="text-sm text-gray-600">{category.percentage}%</span>
                      <span className="text-sm font-medium text-gray-900">{category.sales}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Customer Insights */}
          <Card className="border-0 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Customer Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                {customerInsights.map((insight, index) => (
                  <div key={index} className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-center mb-2">
                      <div className="bg-blue-100 p-2 rounded-lg">
                        <insight.icon className="h-5 w-5 text-blue-600" />
                      </div>
                    </div>
                    <p className="text-lg font-bold text-gray-900">{insight.value}</p>
                    <p className="text-xs text-gray-600">{insight.description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Business Recommendations */}
        <Card className="border-0 shadow-sm border-l-4 border-l-blue-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <Target className="h-5 w-5 mr-2" />
              Business Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                <div className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">
                  1
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 text-sm">Restock Critical Items</h4>
                  <p className="text-sm text-gray-600">
                    8 products are critically low. Restock Sunlight Dishwashing Liquid and White Star Maize Meal
                    immediately.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
                <div className="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">
                  2
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 text-sm">Promote High-Margin Products</h4>
                  <p className="text-sm text-gray-600">
                    White Star Maize Meal has 30.8% margin. Consider featuring it more prominently to boost profits.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3 p-3 bg-orange-50 rounded-lg">
                <div className="bg-orange-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">
                  3
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 text-sm">Optimize Peak Hours</h4>
                  <p className="text-sm text-gray-600">
                    Sales peak at 2PM-6PM. Consider staffing adjustments and promotional activities during these hours.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
