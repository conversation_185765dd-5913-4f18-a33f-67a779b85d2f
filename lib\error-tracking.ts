// Error tracking and monitoring utilities

export interface ErrorEvent {
  id: string
  timestamp: number
  message: string
  stack?: string
  url: string
  userAgent: string
  userId?: string
  sessionId: string
  severity: "low" | "medium" | "high" | "critical"
  category: "javascript" | "network" | "payment" | "auth" | "database" | "ui"
  metadata?: Record<string, any>
}

export interface PerformanceMetric {
  id: string
  timestamp: number
  metric: "page_load" | "api_call" | "render_time" | "interaction"
  value: number
  url: string
  userId?: string
  sessionId: string
  metadata?: Record<string, any>
}

class ErrorTracker {
  private sessionId: string
  private userId?: string
  private errors: ErrorEvent[] = []
  private metrics: PerformanceMetric[] = []
  private isProduction: boolean

  constructor() {
    this.sessionId = this.generateSessionId()
    this.isProduction = process.env.NODE_ENV === "production"
    this.setupGlobalErrorHandlers()
    this.setupPerformanceMonitoring()
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2)}`
  }

  setUserId(userId: string) {
    this.userId = userId
  }

  private setupGlobalErrorHandlers() {
    if (typeof window === "undefined") return

    // JavaScript errors
    window.addEventListener("error", (event) => {
      this.captureError({
        message: event.message,
        stack: event.error?.stack,
        url: event.filename || window.location.href,
        severity: "high",
        category: "javascript",
        metadata: {
          lineno: event.lineno,
          colno: event.colno,
        },
      })
    })

    // Unhandled promise rejections
    window.addEventListener("unhandledrejection", (event) => {
      this.captureError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        url: window.location.href,
        severity: "high",
        category: "javascript",
        metadata: {
          reason: event.reason,
        },
      })
    })

    // Network errors (fetch failures)
    const originalFetch = window.fetch
    window.fetch = async (...args) => {
      const startTime = performance.now()
      try {
        const response = await originalFetch(...args)
        const endTime = performance.now()
        
        // Track API performance
        this.captureMetric({
          metric: "api_call",
          value: endTime - startTime,
          url: args[0]?.toString() || "unknown",
          metadata: {
            status: response.status,
            method: args[1]?.method || "GET",
          },
        })

        if (!response.ok) {
          this.captureError({
            message: `HTTP ${response.status}: ${response.statusText}`,
            url: args[0]?.toString() || "unknown",
            severity: response.status >= 500 ? "high" : "medium",
            category: "network",
            metadata: {
              status: response.status,
              method: args[1]?.method || "GET",
            },
          })
        }

        return response
      } catch (error) {
        const endTime = performance.now()
        this.captureError({
          message: `Network Error: ${error}`,
          stack: error instanceof Error ? error.stack : undefined,
          url: args[0]?.toString() || "unknown",
          severity: "high",
          category: "network",
          metadata: {
            method: args[1]?.method || "GET",
            duration: endTime - startTime,
          },
        })
        throw error
      }
    }
  }

  private setupPerformanceMonitoring() {
    if (typeof window === "undefined") return

    // Page load performance
    window.addEventListener("load", () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming
        if (navigation) {
          this.captureMetric({
            metric: "page_load",
            value: navigation.loadEventEnd - navigation.fetchStart,
            url: window.location.href,
            metadata: {
              domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
              firstPaint: this.getFirstPaint(),
            },
          })
        }
      }, 0)
    })

    // Monitor long tasks
    if ("PerformanceObserver" in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > 50) { // Tasks longer than 50ms
              this.captureMetric({
                metric: "render_time",
                value: entry.duration,
                url: window.location.href,
                metadata: {
                  entryType: entry.entryType,
                  name: entry.name,
                },
              })
            }
          }
        })
        observer.observe({ entryTypes: ["longtask"] })
      } catch (e) {
        // PerformanceObserver not supported
      }
    }
  }

  private getFirstPaint(): number | undefined {
    const paintEntries = performance.getEntriesByType("paint")
    const firstPaint = paintEntries.find(entry => entry.name === "first-paint")
    return firstPaint?.startTime
  }

  captureError(error: Partial<ErrorEvent>) {
    const errorEvent: ErrorEvent = {
      id: `error_${Date.now()}_${Math.random().toString(36).substring(2)}`,
      timestamp: Date.now(),
      message: error.message || "Unknown error",
      stack: error.stack,
      url: error.url || (typeof window !== "undefined" ? window.location.href : "unknown"),
      userAgent: typeof navigator !== "undefined" ? navigator.userAgent : "unknown",
      userId: this.userId,
      sessionId: this.sessionId,
      severity: error.severity || "medium",
      category: error.category || "javascript",
      metadata: error.metadata,
    }

    this.errors.push(errorEvent)
    
    // Log to console in development
    if (!this.isProduction) {
      console.error("Error tracked:", errorEvent)
    }

    // Send to monitoring service (implement based on your choice)
    this.sendToMonitoringService(errorEvent)
  }

  captureMetric(metric: Partial<PerformanceMetric>) {
    const performanceMetric: PerformanceMetric = {
      id: `metric_${Date.now()}_${Math.random().toString(36).substring(2)}`,
      timestamp: Date.now(),
      metric: metric.metric || "interaction",
      value: metric.value || 0,
      url: metric.url || (typeof window !== "undefined" ? window.location.href : "unknown"),
      userId: this.userId,
      sessionId: this.sessionId,
      metadata: metric.metadata,
    }

    this.metrics.push(performanceMetric)

    // Log to console in development
    if (!this.isProduction) {
      console.log("Metric tracked:", performanceMetric)
    }

    // Send to monitoring service
    this.sendMetricToService(performanceMetric)
  }

  private async sendToMonitoringService(error: ErrorEvent) {
    try {
      // In production, send to your monitoring service (e.g., Sentry, LogRocket, etc.)
      // For now, we'll store in localStorage for demo purposes
      if (typeof window !== "undefined") {
        const existingErrors = JSON.parse(localStorage.getItem("spaza_errors") || "[]")
        existingErrors.push(error)
        // Keep only last 100 errors
        if (existingErrors.length > 100) {
          existingErrors.splice(0, existingErrors.length - 100)
        }
        localStorage.setItem("spaza_errors", JSON.stringify(existingErrors))
      }
    } catch (e) {
      console.error("Failed to send error to monitoring service:", e)
    }
  }

  private async sendMetricToService(metric: PerformanceMetric) {
    try {
      // In production, send to your analytics service
      if (typeof window !== "undefined") {
        const existingMetrics = JSON.parse(localStorage.getItem("spaza_metrics") || "[]")
        existingMetrics.push(metric)
        // Keep only last 100 metrics
        if (existingMetrics.length > 100) {
          existingMetrics.splice(0, existingMetrics.length - 100)
        }
        localStorage.setItem("spaza_metrics", JSON.stringify(existingMetrics))
      }
    } catch (e) {
      console.error("Failed to send metric to service:", e)
    }
  }

  // Get stored errors for dashboard
  getStoredErrors(): ErrorEvent[] {
    if (typeof window === "undefined") return []
    try {
      return JSON.parse(localStorage.getItem("spaza_errors") || "[]")
    } catch {
      return []
    }
  }

  // Get stored metrics for dashboard
  getStoredMetrics(): PerformanceMetric[] {
    if (typeof window === "undefined") return []
    try {
      return JSON.parse(localStorage.getItem("spaza_metrics") || "[]")
    } catch {
      return []
    }
  }

  // Clear stored data
  clearStoredData() {
    if (typeof window !== "undefined") {
      localStorage.removeItem("spaza_errors")
      localStorage.removeItem("spaza_metrics")
    }
    this.errors = []
    this.metrics = []
  }
}

// Create singleton instance
export const errorTracker = new ErrorTracker()

// Convenience functions
export const captureError = (error: Partial<ErrorEvent>) => errorTracker.captureError(error)
export const captureMetric = (metric: Partial<PerformanceMetric>) => errorTracker.captureMetric(metric)
export const setUserId = (userId: string) => errorTracker.setUserId(userId)

// React hook for error tracking
export function useErrorTracking() {
  return {
    captureError,
    captureMetric,
    setUserId,
    getStoredErrors: () => errorTracker.getStoredErrors(),
    getStoredMetrics: () => errorTracker.getStoredMetrics(),
    clearStoredData: () => errorTracker.clearStoredData(),
  }
}
