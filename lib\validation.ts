// Input validation and sanitization utilities

import { z } from "zod"

// Common validation schemas
export const emailSchema = z.string()
  .email("Please enter a valid email address")
  .min(1, "Email is required")
  .max(254, "Email is too long")

export const passwordSchema = z.string()
  .min(8, "Password must be at least 8 characters")
  .max(128, "Password is too long")
  .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
  .regex(/[a-z]/, "Password must contain at least one lowercase letter")
  .regex(/[0-9]/, "Password must contain at least one number")
  .regex(/[^A-Za-z0-9]/, "Password must contain at least one special character")

export const nameSchema = z.string()
  .min(1, "Name is required")
  .max(100, "Name is too long")
  .regex(/^[a-zA-Z\s'-]+$/, "Name can only contain letters, spaces, hyphens, and apostrophes")

export const phoneSchema = z.string()
  .regex(/^(\+27|0)[0-9]{9}$/, "Please enter a valid South African phone number")

export const addressSchema = z.string()
  .min(10, "Address must be at least 10 characters")
  .max(500, "Address is too long")

export const priceSchema = z.number()
  .min(0, "Price cannot be negative")
  .max(100000, "Price is too high")

export const quantitySchema = z.number()
  .int("Quantity must be a whole number")
  .min(1, "Quantity must be at least 1")
  .max(1000, "Quantity is too high")

// User registration validation
export const registerSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string(),
  role: z.enum(["customer", "shop_owner"]),
  phone: phoneSchema.optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

// User login validation
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, "Password is required"),
})

// Product validation
export const productSchema = z.object({
  name: z.string().min(1, "Product name is required").max(200, "Product name is too long"),
  description: z.string().max(1000, "Description is too long").optional(),
  price: priceSchema,
  category: z.string().min(1, "Category is required"),
  image: z.string().url("Please enter a valid image URL").optional(),
  inStock: z.boolean(),
  stockQuantity: quantitySchema.optional(),
})

// Order validation
export const orderSchema = z.object({
  items: z.array(z.object({
    productId: z.string(),
    quantity: quantitySchema,
    price: priceSchema,
  })).min(1, "Order must contain at least one item"),
  deliveryAddress: addressSchema,
  deliveryOption: z.enum(["standard", "express", "pickup"]),
  notes: z.string().max(500, "Notes are too long").optional(),
})

// Cart item validation
export const cartItemSchema = z.object({
  productId: z.string(),
  quantity: quantitySchema,
})

// Payment validation
export const paymentSchema = z.object({
  method: z.enum(["card", "cash_on_delivery", "eft", "mobile_money", "store_credit"]),
  amount: priceSchema,
  currency: z.literal("ZAR"),
})

// Card payment details validation
export const cardDetailsSchema = z.object({
  number: z.string()
    .regex(/^[0-9]{13,19}$/, "Please enter a valid card number")
    .transform(val => val.replace(/\s/g, "")),
  expiry: z.string()
    .regex(/^(0[1-9]|1[0-2])\/([0-9]{2})$/, "Please enter expiry in MM/YY format"),
  cvv: z.string()
    .regex(/^[0-9]{3,4}$/, "Please enter a valid CVV"),
  name: nameSchema,
})

// Mobile money validation
export const mobileMoneySchema = z.object({
  phoneNumber: phoneSchema,
  provider: z.enum(["mtn", "vodacom"]),
})

// Sanitization functions
export function sanitizeString(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, "") // Remove potential HTML tags
    .replace(/javascript:/gi, "") // Remove javascript: protocol
    .replace(/on\w+=/gi, "") // Remove event handlers
}

export function sanitizeEmail(email: string): string {
  return email.toLowerCase().trim()
}

export function sanitizePhone(phone: string): string {
  // Remove all non-digit characters except +
  const cleaned = phone.replace(/[^\d+]/g, "")
  
  // Convert local format to international
  if (cleaned.startsWith("0")) {
    return "+27" + cleaned.substring(1)
  }
  
  return cleaned
}

export function sanitizePrice(price: number): number {
  // Round to 2 decimal places
  return Math.round(price * 100) / 100
}

// Rate limiting utilities
interface RateLimitEntry {
  count: number
  resetTime: number
}

class RateLimiter {
  private limits: Map<string, RateLimitEntry> = new Map()
  private readonly windowMs: number
  private readonly maxRequests: number

  constructor(windowMs: number = 60000, maxRequests: number = 100) {
    this.windowMs = windowMs
    this.maxRequests = maxRequests
  }

  isAllowed(identifier: string): boolean {
    const now = Date.now()
    const entry = this.limits.get(identifier)

    if (!entry || now > entry.resetTime) {
      // Reset or create new entry
      this.limits.set(identifier, {
        count: 1,
        resetTime: now + this.windowMs
      })
      return true
    }

    if (entry.count >= this.maxRequests) {
      return false
    }

    entry.count++
    return true
  }

  getRemainingRequests(identifier: string): number {
    const entry = this.limits.get(identifier)
    if (!entry || Date.now() > entry.resetTime) {
      return this.maxRequests
    }
    return Math.max(0, this.maxRequests - entry.count)
  }

  getResetTime(identifier: string): number {
    const entry = this.limits.get(identifier)
    if (!entry || Date.now() > entry.resetTime) {
      return Date.now() + this.windowMs
    }
    return entry.resetTime
  }

  cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.limits.entries()) {
      if (now > entry.resetTime) {
        this.limits.delete(key)
      }
    }
  }
}

// Create rate limiters for different operations
export const authRateLimiter = new RateLimiter(15 * 60 * 1000, 5) // 5 attempts per 15 minutes
export const apiRateLimiter = new RateLimiter(60 * 1000, 100) // 100 requests per minute
export const paymentRateLimiter = new RateLimiter(60 * 1000, 10) // 10 payment attempts per minute

// CSRF protection
export function generateCSRFToken(): string {
  const array = new Uint8Array(32)
  crypto.getRandomValues(array)
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
}

export function validateCSRFToken(token: string, storedToken: string): boolean {
  if (!token || !storedToken) return false
  return token === storedToken
}

// Content Security Policy
export const CSP_HEADER = [
  "default-src 'self'",
  "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com",
  "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
  "font-src 'self' https://fonts.gstatic.com",
  "img-src 'self' data: https: blob:",
  "connect-src 'self' https://api.stripe.com https://*.convex.cloud wss://*.convex.cloud",
  "frame-src 'self' https://js.stripe.com",
  "object-src 'none'",
  "base-uri 'self'",
  "form-action 'self'",
  "frame-ancestors 'none'",
  "upgrade-insecure-requests"
].join("; ")

// Security headers
export const SECURITY_HEADERS = {
  "Content-Security-Policy": CSP_HEADER,
  "X-Frame-Options": "DENY",
  "X-Content-Type-Options": "nosniff",
  "Referrer-Policy": "strict-origin-when-cross-origin",
  "Permissions-Policy": "camera=(), microphone=(), geolocation=()",
  "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
}

// Input validation helper
export function validateInput<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; errors: string[] } {
  try {
    const result = schema.parse(data)
    return { success: true, data: result }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(err => err.message)
      }
    }
    return {
      success: false,
      errors: ["Validation failed"]
    }
  }
}
